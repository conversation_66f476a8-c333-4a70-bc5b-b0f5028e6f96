# AI实时同声传译应用

一个基于Web技术的响应式实时同声传译应用，支持桌面端和移动端，使用豆包同传API提供高质量的语音识别和翻译服务。

## 功能特性

### 🎯 核心功能
- **实时语音识别**：基于Web Audio API的高质量音频捕获
- **同声传译**：支持多语言实时翻译
- **响应式设计**：完美适配桌面端和移动端
- **离线支持**：PWA技术，支持离线使用
- **触控优化**：针对移动设备的触控交互优化

### 🌐 支持的语言
- 英语 ↔ 中文
- 中文 ↔ 英语
- 日语 ↔ 中文
- 韩语 ↔ 中文
- 更多语言对持续添加中...

### 📱 设备兼容性
- **桌面端**：Chrome 88+, Firefox 85+, Safari 14+, Edge 88+
- **移动端**：iOS Safari 14+, Android Chrome 88+
- **PWA支持**：可安装为原生应用

## 快速开始

### 1. 获取API密钥
1. 访问 [豆包开放平台](https://www.volcengine.com/product/doubao)
2. 注册账号并创建应用
3. 获取同声传译API密钥

### 2. 部署应用
```bash
# 克隆项目
git clone <repository-url>
cd real-tran

# 使用HTTP服务器运行（必须使用HTTPS或localhost）
# 方式1：使用Python
python -m http.server 8000

# 方式2：使用Node.js
npx serve .

# 方式3：使用Live Server (VS Code扩展)
# 右键index.html -> Open with Live Server
```

### 3. 配置和使用
1. 打开浏览器访问 `http://localhost:8000`
2. 点击设置按钮，输入API密钥
3. 选择源语言和目标语言
4. 允许麦克风权限
5. 点击开始录音按钮开始翻译

## 项目结构

```
real-tran/
├── index.html              # 主页面
├── manifest.json           # PWA配置
├── sw.js                   # Service Worker
├── css/
│   ├── main.css           # 主样式
│   ├── responsive.css     # 响应式样式
│   └── themes.css         # 主题样式
├── js/
│   ├── utils.js           # 工具函数
│   ├── audio-capture.js   # 音频捕获
│   ├── audio-processor.js # 音频处理
│   ├── websocket-client.js # WebSocket客户端
│   ├── ui-controller.js   # UI控制器
│   └── app.js             # 主应用
└── assets/
    └── icons/             # 应用图标
```

## 技术架构

### 前端技术栈
- **HTML5**：语义化标记，无障碍支持
- **CSS3**：现代CSS特性，响应式设计
- **JavaScript ES6+**：模块化架构，事件驱动
- **Web Audio API**：实时音频处理
- **WebSocket**：实时通信
- **PWA**：渐进式Web应用

### 核心模块
1. **AudioCapture**：音频捕获和设备管理
2. **AudioProcessor**：音频格式转换和处理
3. **WebSocketClient**：API通信和连接管理
4. **UIController**：用户界面交互逻辑
5. **RealTranApp**：主应用协调器

### 音频处理流程
```
麦克风输入 → Web Audio API → 音频重采样(16kHz) → 
PCM16格式转换 → Base64编码 → WebSocket传输 → 
豆包API处理 → 翻译结果返回 → UI显示
```

## 使用说明

### 基本操作
1. **开始翻译**：点击录音按钮或按 `Ctrl+Enter`
2. **停止翻译**：点击停止按钮或再次按 `Ctrl+Enter`
3. **语言切换**：使用语言选择器或点击交换按钮
4. **清空文本**：点击清空按钮
5. **复制译文**：点击复制按钮或按 `Ctrl+C`

### 设置选项
- **API密钥**：豆包同传API密钥
- **音频设备**：选择麦克风设备
- **主题模式**：浅色/深色主题切换
- **字体大小**：调整文本显示大小
- **自动滚动**：自动滚动到最新内容
- **提示音**：翻译完成提示音

### 键盘快捷键
- `Ctrl+Enter`：开始/停止录音
- `Ctrl+,`：打开设置
- `Escape`：关闭设置
- `Ctrl+C`：复制译文（焦点在译文区域时）

## 移动端优化

### 触控交互
- 增大触控目标（最小48px）
- 触控反馈动画
- 手势支持

### 屏幕适配
- 安全区域适配（刘海屏等）
- 横竖屏自动适配
- 可折叠设备支持

### 性能优化
- 电池优化模式
- 减少动画（用户偏好）
- 懒加载和缓存

## 浏览器兼容性

### 必需功能支持
- Web Audio API
- MediaStream API
- WebSocket
- ES6+ JavaScript
- CSS Grid/Flexbox

### 推荐浏览器版本
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 故障排除

### 常见问题

**Q: 无法访问麦克风**
A: 确保使用HTTPS或localhost，并允许麦克风权限

**Q: 连接失败**
A: 检查API密钥是否正确，网络连接是否正常

**Q: 音频质量差**
A: 检查麦克风设备，确保环境安静

**Q: 翻译延迟高**
A: 检查网络连接，尝试更换网络环境

**Q: 移动端显示异常**
A: 尝试刷新页面，或清除浏览器缓存

### 调试模式
打开浏览器开发者工具，查看控制台输出获取详细错误信息。

## 开发指南

### 本地开发
```bash
# 启动开发服务器
npm run dev

# 或使用Python
python -m http.server 8000
```

### 代码规范
- 使用ES6+语法
- 模块化设计
- 事件驱动架构
- 错误处理完善

### 测试
- 在不同设备上测试
- 检查浏览器兼容性
- 验证音频质量
- 测试网络异常情况

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的同声传译功能
- 响应式设计
- PWA支持
