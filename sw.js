/**
 * Service Worker
 * 提供离线支持、缓存管理和后台同步功能
 */

const CACHE_NAME = 'real-tran-v1.0.0';
const STATIC_CACHE_NAME = 'real-tran-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'real-tran-dynamic-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/manifest.json',
    '/css/main.css',
    '/css/responsive.css',
    '/css/themes.css',
    '/js/utils.js',
    '/js/audio-capture.js',
    '/js/audio-processor.js',
    '/js/websocket-client.js',
    '/js/ui-controller.js',
    '/js/app.js',
    '/assets/icons/favicon.svg',
    '/assets/icons/icon-192x192.png',
    '/assets/icons/icon-512x512.png'
];

// 需要网络优先的资源
const NETWORK_FIRST_PATTERNS = [
    /\/api\//,
    /\.json$/,
    /websocket/
];

// 缓存优先的资源
const CACHE_FIRST_PATTERNS = [
    /\.css$/,
    /\.js$/,
    /\.png$/,
    /\.jpg$/,
    /\.jpeg$/,
    /\.svg$/,
    /\.ico$/,
    /\.woff$/,
    /\.woff2$/
];

// Service Worker安装事件
self.addEventListener('install', (event) => {
    console.log('Service Worker: 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: 缓存静态资源');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker: 安装完成');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: 安装失败', error);
            })
    );
});

// Service Worker激活事件
self.addEventListener('activate', (event) => {
    console.log('Service Worker: 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        // 删除旧版本的缓存
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME &&
                            cacheName.startsWith('real-tran-')) {
                            console.log('Service Worker: 删除旧缓存', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: 激活完成');
                return self.clients.claim();
            })
    );
});

// 拦截网络请求
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 跳过非HTTP请求
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // 跳过WebSocket请求
    if (request.url.includes('websocket') || request.url.includes('wss://')) {
        return;
    }
    
    // 根据请求类型选择缓存策略
    if (isNetworkFirst(request.url)) {
        event.respondWith(networkFirst(request));
    } else if (isCacheFirst(request.url)) {
        event.respondWith(cacheFirst(request));
    } else {
        event.respondWith(staleWhileRevalidate(request));
    }
});

// 判断是否为网络优先资源
function isNetworkFirst(url) {
    return NETWORK_FIRST_PATTERNS.some(pattern => pattern.test(url));
}

// 判断是否为缓存优先资源
function isCacheFirst(url) {
    return CACHE_FIRST_PATTERNS.some(pattern => pattern.test(url));
}

// 网络优先策略
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        // 如果网络请求成功，更新缓存
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Service Worker: 网络请求失败，尝试从缓存获取', request.url);
        
        // 网络失败时从缓存获取
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // 如果是导航请求且缓存中没有，返回离线页面
        if (request.mode === 'navigate') {
            return caches.match('/');
        }
        
        throw error;
    }
}

// 缓存优先策略
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Service Worker: 缓存优先策略失败', request.url, error);
        throw error;
    }
}

// 过期重新验证策略
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    // 后台更新缓存
    const fetchPromise = fetch(request).then((networkResponse) => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch((error) => {
        console.log('Service Worker: 后台更新失败', request.url);
    });
    
    // 如果有缓存，立即返回缓存，否则等待网络请求
    return cachedResponse || fetchPromise;
}

// 处理消息事件
self.addEventListener('message', (event) => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_VERSION':
            event.ports[0].postMessage({
                version: CACHE_NAME
            });
            break;
            
        case 'CLEAR_CACHE':
            clearAllCaches().then(() => {
                event.ports[0].postMessage({ success: true });
            }).catch((error) => {
                event.ports[0].postMessage({ success: false, error: error.message });
            });
            break;
            
        default:
            console.log('Service Worker: 未知消息类型', type);
    }
});

// 清除所有缓存
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
}

// 后台同步事件（如果支持）
if ('sync' in self.registration) {
    self.addEventListener('sync', (event) => {
        console.log('Service Worker: 后台同步', event.tag);
        
        if (event.tag === 'background-sync') {
            event.waitUntil(doBackgroundSync());
        }
    });
}

// 执行后台同步
async function doBackgroundSync() {
    try {
        // 这里可以添加后台同步逻辑
        // 例如：上传离线时保存的数据
        console.log('Service Worker: 执行后台同步');
    } catch (error) {
        console.error('Service Worker: 后台同步失败', error);
    }
}

// 推送通知事件（如果需要）
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body || '您有新的翻译结果',
            icon: '/assets/icons/icon-192x192.png',
            badge: '/assets/icons/badge-72x72.png',
            tag: 'real-tran-notification',
            requireInteraction: false,
            actions: [
                {
                    action: 'open',
                    title: '查看',
                    icon: '/assets/icons/action-open.png'
                },
                {
                    action: 'close',
                    title: '关闭',
                    icon: '/assets/icons/action-close.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || 'AI同传', options)
        );
    }
});

// 通知点击事件
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.action === 'open') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// 错误处理
self.addEventListener('error', (event) => {
    console.error('Service Worker: 全局错误', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker: 未处理的Promise拒绝', event.reason);
});

console.log('Service Worker: 脚本加载完成');
