# 我用AI同传干掉了英语发布会，爽。

我之前看各种什么OpenAI、Google等等的发布会，还有各种线下的英语演讲的时候，一直有一个痛点。就是，我听不懂。大多数的发布会是直播，所以Youtube上也没有原生字幕可以看，线下演讲更是这样，好一点的会务会给你准备同传翻译机或者搞个副屏，放AI字幕。前几天我去参加WAIC的论坛就有这个同传翻译机。

但是很多的时候，可能并没有这么好的条件，就是啥也没有，需要你自己听。虽然我不太应该这么理直气壮，因为从小没好好学英语，导致我英语很烂，这确实是我自己不努力造成的= =但是吧，到现在，因为自己一直在玩AI的原因，最高质量的AI信息和资讯，还是来自于英文世界。这些AI知识我必须要去学才行。

坦诚的讲，虽然华裔面孔占据AI世界主流，但是主流语言还是英文。这就导致我每次看发布会或者线下演讲，都只能软件开着字幕进行翻译。

线上看发布会就像这样。线下我现在就是直接开个飞书妙记，去实时转录+翻译。其实已经很好用了，对吧，但是字幕类的我自己用的还是不爽，因为这代表着，你感受不到对方的情绪和状态。同时，你也没办法一心二用。看发布会，你只能不断的盯着字幕，干不了任何别的事情。

在会场上听演讲也是，最der的就是。你低头看翻译，你就看不了嘉宾和PPT，你抬头看嘉宾和PPT，你就听不懂他在说什么。。。线上看直播一样也是这个道理。

这次WAIC现场里听的英文演讲，实在是让我太痛苦了。当时在现场，我就在想，有没有什么方法，能手搓一个不需要我盯着看的AI同传小产品，来解决我的这些痛点。

回北京以后，说干就干。大概思路特别简单，我做一个浏览器插件和一个小网页，然后接一个AI同传的API，就搞定了。

## 首先是同传API

我基本上把全网都找遍了。发现做AI同传大模型的也没几个。讯飞、百度、豆包、腾讯、阿里、Gemini，好像就没了。。。

最后我选了豆包，因为我自己的一些服务都在火山引擎上，字节家的更顺，而且他们豆包同声传译2.0是刚发的，效果也确实是目前最棒的。端到端模型，延迟大概2~3秒，基本一句话说完，对应的翻译就出来了。整体翻译质量也是OK的。

还有一个非常屌的能力，是可以在不采集声音样本的情况下复刻说话人的音色，而且就算是多个人一起对话，每一个人都可以保持自己的原色来进行同传，音色极其自然。这个是传统的所有的机器翻译的模型所不具备的。

因为比如像现在OpenAI、Grok啥的发布会，每次都是好几个人在现场，我其实是需要知道到底是谁说了啥的。而借助豆包同传2.0，这个问题被完美解决。

你也可以直接在这个地方体验一下，可以免费用5分钟，每天可以20次，也就是每天免费100分钟的额度。
https://console.volcengine.com/ark/region:ark+cn-beijing/experience/voice?type=SI

而API这块，价格也还OK。实测下来，1分钟大概消耗了1800左右的Token。按照官方价格换算，大概就是一分钟3毛钱，很便宜了。

## 技术实现挑战

模型有了，接下来，就是搓一个小浏览器插件和网页，然后把模型的API接进去。这个过程，我本来以为非常轻松，毕竟一个浏览器插件和网页，这玩意vibe coding搞过N个了，没啥难的的，几个按钮，一个设置，能有一个填模型Key的地方，不就完事了吗。

在浏览器里直接抓取正在播放的视频音频，然后调用豆包同传2.0的API，实时翻译成中文，再播放出来。听起来完美，对不对？

但是，噩梦开始了。。。我真的感觉到了，当时什么叫无知者无畏。

我一开始就卡在了第一步，怎么在浏览器插件里调用豆包同传2.0模型的API？要知道，这是同传模型，不是普通的那种对话的大模型。这个API用的是WebSocket协议，还需要在请求头里加认证信息。。。

就是，你平时刷网页，大多是"问一句答一句"，就像发个短信一样，你发了个请求，"给我一个网页"，服务器回了你，"好的，这是你要的页面"。之后呢？之后就各忙各的了。如果你想要更新信息，那就得再发一次请求，再等一次回复。这就是普通的HTTP协议，简单但效率一般。

但有时候，我们不想每次都问一下才有回应，我们想要的是实时互动，比如你跟朋友打电话，不需要每次想说话的时候再拨一次号码。WebSocket协议就是为这个场景而生的。它更像一通电话，一旦你接通了，双方可以一直不停地实时说话，不用再挂掉重拨。

豆包这个API用的就是WebSocket协议，因为同声传译这个场景，恰好需要你不断地把音频数据发送过去，服务器一边接收一边实时返回翻译后的文字或音频，你一边说，它一边翻译，非常顺畅。

而最大的问题是，浏览器插件在调用WebSocket的时候，有很多安全限制和跨域问题需要解决。

## 浏览器音频捕获的挑战

除了API调用的问题，还有一个更大的技术难点：如何在浏览器中捕获正在播放的音频？

这个问题比想象中复杂得多。浏览器出于安全考虑，不允许网页或插件随意获取系统音频。你不能简单地说"给我当前播放的所有声音"，因为这可能涉及隐私问题。

通常情况下，浏览器只允许你获取麦克风的输入，或者是用户明确授权的媒体流。但我们需要的是捕获网页中正在播放的视频音频，这就需要一些特殊的技术手段。

经过大量的研究和尝试，我发现了几种可能的解决方案：

1. **使用Web Audio API**：可以通过createMediaElementSource()方法来获取audio或video元素的音频流
2. **Chrome扩展的tabCapture API**：可以捕获标签页的音频和视频流
3. **屏幕共享API**：通过getDisplayMedia()获取屏幕和音频

最终我选择了Chrome扩展的tabCapture API，因为它专门为这种场景设计，可以直接获取标签页的音频流，而且权限控制相对简单。

## 实际开发过程

有了技术方案，接下来就是具体的开发实现。整个系统分为几个部分：

1. **Chrome浏览器插件**：负责捕获音频和用户界面
2. **后端服务**：处理WebSocket连接和API调用
3. **音频处理模块**：格式转换和流处理

### Chrome插件部分

插件的manifest.json需要申请必要的权限：

```json
{
  "permissions": [
    "tabCapture",
    "activeTab",
    "storage"
  ]
}
```

核心的音频捕获代码：

```javascript
chrome.tabCapture.capture({
  audio: true,
  video: false
}, function(stream) {
  // 处理音频流
  processAudioStream(stream);
});
```

### 后端服务

由于浏览器插件无法直接处理WebSocket的认证和复杂协议，我搭建了一个简单的Node.js后端服务作为中转：

```javascript
const WebSocket = require('ws');
const express = require('express');

// 创建HTTP服务器用于接收插件的音频数据
const app = express();

// 创建WebSocket连接到豆包API
const ws = new WebSocket('wss://openspeech.bytedance.com/api/v1/sti', {
  headers: {
    'Authorization': `Bearer ${API_KEY}`
  }
});
```

### 音频处理

最复杂的部分是音频格式的处理。豆包API要求特定的音频格式（16kHz采样率，16位PCM），而浏览器捕获的音频通常是48kHz的。

需要进行实时的音频重采样：

```javascript
function resampleAudio(audioBuffer, targetSampleRate) {
  const ratio = audioBuffer.sampleRate / targetSampleRate;
  const newLength = Math.round(audioBuffer.length / ratio);
  const result = new Float32Array(newLength);
  
  for (let i = 0; i < newLength; i++) {
    const index = i * ratio;
    const indexInt = Math.floor(index);
    const indexFrac = index - indexInt;
    
    if (indexInt < audioBuffer.length - 1) {
      result[i] = audioBuffer[indexInt] * (1 - indexFrac) + 
                  audioBuffer[indexInt + 1] * indexFrac;
    } else {
      result[i] = audioBuffer[indexInt];
    }
  }
  
  return result;
}
```

## 最终效果

经过几天的折腾，终于搞定了这个AI同传小工具。使用效果超出预期：

1. **延迟控制**：整体延迟在3-4秒左右，基本可以接受
2. **翻译质量**：豆包的翻译质量确实不错，专业术语翻译也比较准确
3. **音色保持**：多人对话时能保持原声音色，体验很自然
4. **使用便捷**：一键启动，无需额外设置

现在看英文发布会终于不用盯着字幕了，可以专心看演讲者的表情和PPT，同时听着流畅的中文翻译。虽然开发过程踩了不少坑，但最终的效果让我很满意。

这个小工具不仅解决了我个人的痛点，也让我对实时音频处理和AI API的集成有了更深的理解。技术的魅力就在于此，看似简单的需求背后往往隐藏着复杂的技术挑战，而解决这些挑战的过程本身就是最大的收获。