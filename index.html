<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="AI实时同声传译 - 支持多语言实时翻译">
    <meta name="keywords" content="同声传译,AI翻译,实时翻译,语音识别">
    <title>AI实时同声传译</title>

    <!-- PWA支持 -->
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="AI同传">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="js/app.js" as="script">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/themes.css">

    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="assets/icons/favicon.svg">
    <link rel="apple-touch-icon" href="assets/icons/icon-180x180.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/icons/icon-16x16.png">
    <link rel="mask-icon" href="assets/icons/safari-pinned-tab.svg" color="#2563eb">
</head>
<body>
    <!-- 主容器 -->
    <div id="app" class="app-container">
        <!-- 顶部状态栏 -->
        <header class="status-bar">
            <div class="status-info">
                <div class="connection-status" id="connectionStatus">
                    <span class="status-indicator" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">未连接</span>
                </div>
                <div class="audio-level" id="audioLevel">
                    <span class="level-label">音量</span>
                    <div class="level-meter">
                        <div class="level-bar" id="levelBar"></div>
                    </div>
                </div>
            </div>
            <div class="header-actions">
                <button class="theme-toggle" id="themeToggle" aria-label="切换主题">
                    <span class="theme-icon">🌙</span>
                </button>
                <button class="settings-toggle" id="settingsToggle" aria-label="设置">
                    <span class="settings-icon">⚙️</span>
                </button>
            </div>
        </header>

        <!-- 主控制区域 -->
        <main class="main-content">
            <!-- 控制面板 -->
            <section class="control-panel">
                <div class="language-selector">
                    <div class="language-group">
                        <label for="sourceLanguage">源语言</label>
                        <select id="sourceLanguage" class="language-select">
                            <option value="zh">中文</option>
                            <option value="en" selected>英文</option>
                            <option value="ja">日文</option>
                            <option value="ko">韩文</option>
                            <option value="fr">法文</option>
                            <option value="de">德文</option>
                            <option value="es">西班牙文</option>
                            <option value="ru">俄文</option>
                        </select>
                    </div>
                    <button class="language-swap" id="languageSwap" aria-label="交换语言">
                        <span class="swap-icon">⇄</span>
                    </button>
                    <div class="language-group">
                        <label for="targetLanguage">目标语言</label>
                        <select id="targetLanguage" class="language-select">
                            <option value="zh" selected>中文</option>
                            <option value="en">英文</option>
                            <option value="ja">日文</option>
                            <option value="ko">韩文</option>
                            <option value="fr">法文</option>
                            <option value="de">德文</option>
                            <option value="es">西班牙文</option>
                            <option value="ru">俄文</option>
                        </select>
                    </div>
                </div>

                <!-- 主控制按钮 -->
                <div class="main-controls">
                    <button class="record-button" id="recordButton" aria-label="开始录音">
                        <span class="record-icon">🎤</span>
                        <span class="record-text">开始翻译</span>
                    </button>
                    <button class="stop-button" id="stopButton" aria-label="停止录音" disabled>
                        <span class="stop-icon">⏹️</span>
                        <span class="stop-text">停止翻译</span>
                    </button>
                </div>
            </section>

            <!-- 翻译显示区域 -->
            <section class="translation-display">
                <div class="text-panels">
                    <!-- 原文面板 -->
                    <div class="text-panel source-panel">
                        <div class="panel-header">
                            <h3 class="panel-title">原文</h3>
                            <button class="clear-button" id="clearSource" aria-label="清空原文">
                                <span class="clear-icon">🗑️</span>
                            </button>
                        </div>
                        <div class="text-content" id="sourceText">
                            <div class="placeholder">等待语音输入...</div>
                        </div>
                    </div>

                    <!-- 译文面板 -->
                    <div class="text-panel target-panel">
                        <div class="panel-header">
                            <h3 class="panel-title">译文</h3>
                            <div class="panel-actions">
                                <button class="copy-button" id="copyTarget" aria-label="复制译文">
                                    <span class="copy-icon">📋</span>
                                </button>
                                <button class="clear-button" id="clearTarget" aria-label="清空译文">
                                    <span class="clear-icon">🗑️</span>
                                </button>
                            </div>
                        </div>
                        <div class="text-content" id="targetText">
                            <div class="placeholder">翻译结果将显示在这里...</div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 设置面板 -->
        <aside class="settings-panel" id="settingsPanel">
            <div class="settings-header">
                <h2>设置</h2>
                <button class="close-settings" id="closeSettings" aria-label="关闭设置">
                    <span class="close-icon">✕</span>
                </button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <label for="apiKey">豆包API密钥</label>
                    <input type="password" id="apiKey" class="setting-input" placeholder="请输入您的API密钥">
                    <small class="setting-help">
                        获取API密钥：<a href="https://console.volcengine.com/ark" target="_blank">火山引擎控制台</a>
                    </small>
                </div>

                <div class="setting-group">
                    <label for="audioDevice">音频设备</label>
                    <select id="audioDevice" class="setting-select">
                        <option value="">默认麦克风</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label class="setting-checkbox">
                        <input type="checkbox" id="autoScroll" checked>
                        <span class="checkmark"></span>
                        自动滚动文本
                    </label>
                </div>

                <div class="setting-group">
                    <label class="setting-checkbox">
                        <input type="checkbox" id="playSound" checked>
                        <span class="checkmark"></span>
                        播放提示音
                    </label>
                </div>

                <div class="setting-group">
                    <label for="fontSize">字体大小</label>
                    <input type="range" id="fontSize" class="setting-range" min="12" max="24" value="16">
                    <span class="range-value" id="fontSizeValue">16px</span>
                </div>
            </div>
        </aside>

        <!-- 遮罩层 -->
        <div class="overlay" id="overlay"></div>

        <!-- 加载指示器 -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="spinner"></div>
            <div class="loading-text">正在连接...</div>
        </div>

        <!-- 错误提示 -->
        <div class="error-toast" id="errorToast">
            <div class="error-content">
                <span class="error-icon">⚠️</span>
                <span class="error-message" id="errorMessage"></span>
                <button class="error-close" id="errorClose">✕</button>
            </div>
        </div>

        <!-- 成功提示 -->
        <div class="success-toast" id="successToast">
            <div class="success-content">
                <span class="success-icon">✅</span>
                <span class="success-message" id="successMessage"></span>
            </div>
        </div>
    </div>

    <!-- JavaScript模块 -->
    <script src="js/utils.js"></script>
    <script src="js/audio-capture.js"></script>
    <script src="js/audio-processor.js"></script>
    <script src="js/websocket-client.js"></script>
    <script src="js/ui-controller.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
