<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI同传 - 功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>AI实时同声传译 - 功能测试</h1>
    
    <div class="test-section">
        <h2>1. 浏览器兼容性检测</h2>
        <button onclick="testBrowserSupport()">检测浏览器支持</button>
        <div id="browserSupport" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 音频设备检测</h2>
        <button onclick="testAudioDevices()">检测音频设备</button>
        <div id="audioDevices" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 麦克风权限测试</h2>
        <button onclick="testMicrophonePermission()">测试麦克风权限</button>
        <div id="microphonePermission" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 音频捕获测试</h2>
        <button onclick="testAudioCapture()">开始音频捕获</button>
        <button onclick="stopAudioCapture()">停止音频捕获</button>
        <div id="audioCapture" class="test-result"></div>
        <canvas id="audioVisualization" width="400" height="100" style="border: 1px solid #ddd; margin-top: 10px;"></canvas>
    </div>
    
    <div class="test-section">
        <h2>5. 音频处理测试</h2>
        <button onclick="testAudioProcessing()">测试音频处理</button>
        <div id="audioProcessing" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>6. WebSocket连接测试</h2>
        <input type="text" id="apiKeyInput" placeholder="输入API密钥" style="width: 300px; padding: 8px; margin: 5px;">
        <button onclick="testWebSocketConnection()">测试WebSocket连接</button>
        <button onclick="disconnectWebSocket()">断开连接</button>
        <div id="webSocketConnection" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>7. 本地存储测试</h2>
        <button onclick="testLocalStorage()">测试本地存储</button>
        <div id="localStorage" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>8. PWA功能测试</h2>
        <button onclick="testPWAFeatures()">测试PWA功能</button>
        <div id="pwaFeatures" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>9. 性能测试</h2>
        <button onclick="testPerformance()">运行性能测试</button>
        <div id="performance" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="testLog" class="log"></div>
    </div>

    <script>
        // 全局变量
        let audioCapture = null;
        let audioProcessor = null;
        let webSocketClient = null;
        let audioContext = null;
        let mediaStream = null;
        let analyser = null;
        let animationId = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            logElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        // 1. 浏览器兼容性检测
        function testBrowserSupport() {
            log('开始浏览器兼容性检测');
            const result = document.getElementById('browserSupport');
            
            const tests = [
                {
                    name: 'getUserMedia API',
                    test: () => navigator.mediaDevices && navigator.mediaDevices.getUserMedia
                },
                {
                    name: 'Web Audio API',
                    test: () => window.AudioContext || window.webkitAudioContext
                },
                {
                    name: 'WebSocket',
                    test: () => window.WebSocket
                },
                {
                    name: 'Service Worker',
                    test: () => 'serviceWorker' in navigator
                },
                {
                    name: 'Local Storage',
                    test: () => window.localStorage
                },
                {
                    name: 'ES6 支持',
                    test: () => {
                        try {
                            eval('class Test {}; const test = () => {}; let x = 1;');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                }
            ];

            let html = '<h3>兼容性检测结果:</h3>';
            let allPassed = true;

            tests.forEach(test => {
                const passed = test.test();
                allPassed = allPassed && passed;
                html += `<div class="${passed ? 'success' : 'error'}">
                    ${test.name}: ${passed ? '✓ 支持' : '✗ 不支持'}
                </div>`;
                log(`${test.name}: ${passed ? '支持' : '不支持'}`, passed ? 'success' : 'error');
            });

            html += `<div class="${allPassed ? 'success' : 'warning'}">
                总体兼容性: ${allPassed ? '✓ 完全兼容' : '⚠ 部分功能可能不可用'}
            </div>`;

            result.innerHTML = html;
            log('浏览器兼容性检测完成');
        }

        // 2. 音频设备检测
        async function testAudioDevices() {
            log('开始音频设备检测');
            const result = document.getElementById('audioDevices');
            
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                
                let html = '<h3>音频输入设备:</h3>';
                
                if (audioInputs.length === 0) {
                    html += '<div class="error">未检测到音频输入设备</div>';
                    log('未检测到音频输入设备', 'error');
                } else {
                    audioInputs.forEach((device, index) => {
                        html += `<div class="success">
                            设备 ${index + 1}: ${device.label || `麦克风 ${device.deviceId.slice(0, 8)}`}
                        </div>`;
                        log(`发现音频设备: ${device.label || device.deviceId}`, 'success');
                    });
                }
                
                result.innerHTML = html;
                log('音频设备检测完成');
            } catch (error) {
                result.innerHTML = `<div class="error">检测失败: ${error.message}</div>`;
                log(`音频设备检测失败: ${error.message}`, 'error');
            }
        }

        // 3. 麦克风权限测试
        async function testMicrophonePermission() {
            log('开始麦克风权限测试');
            const result = document.getElementById('microphonePermission');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                
                result.innerHTML = '<div class="success">✓ 麦克风权限已获取</div>';
                log('麦克风权限获取成功', 'success');
            } catch (error) {
                let message = '麦克风权限获取失败';
                if (error.name === 'NotAllowedError') {
                    message = '用户拒绝了麦克风权限';
                } else if (error.name === 'NotFoundError') {
                    message = '未找到音频输入设备';
                } else if (error.name === 'NotSupportedError') {
                    message = '浏览器不支持音频捕获';
                }
                
                result.innerHTML = `<div class="error">✗ ${message}</div>`;
                log(message, 'error');
            }
        }

        // 4. 音频捕获测试
        async function testAudioCapture() {
            log('开始音频捕获测试');
            const result = document.getElementById('audioCapture');
            
            try {
                // 创建音频上下文
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                // 获取音频流
                mediaStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 48000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });
                
                // 创建分析器
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                
                const source = audioContext.createMediaStreamSource(mediaStream);
                source.connect(analyser);
                
                // 开始可视化
                startAudioVisualization();
                
                result.innerHTML = '<div class="success">✓ 音频捕获成功，正在显示音频波形</div>';
                log('音频捕获成功', 'success');
                
            } catch (error) {
                result.innerHTML = `<div class="error">✗ 音频捕获失败: ${error.message}</div>`;
                log(`音频捕获失败: ${error.message}`, 'error');
            }
        }

        function stopAudioCapture() {
            log('停止音频捕获');
            
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
            }
            
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            
            // 清空画布
            const canvas = document.getElementById('audioVisualization');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            document.getElementById('audioCapture').innerHTML = '<div class="warning">音频捕获已停止</div>';
            log('音频捕获已停止');
        }

        function startAudioVisualization() {
            const canvas = document.getElementById('audioVisualization');
            const ctx = canvas.getContext('2d');
            const bufferLength = analyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            
            function draw() {
                animationId = requestAnimationFrame(draw);
                
                analyser.getByteFrequencyData(dataArray);
                
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                const barWidth = (canvas.width / bufferLength) * 2.5;
                let barHeight;
                let x = 0;
                
                for (let i = 0; i < bufferLength; i++) {
                    barHeight = (dataArray[i] / 255) * canvas.height;
                    
                    ctx.fillStyle = `rgb(${barHeight + 100}, 50, 50)`;
                    ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
                    
                    x += barWidth + 1;
                }
            }
            
            draw();
        }

        // 5. 音频处理测试
        function testAudioProcessing() {
            log('开始音频处理测试');
            const result = document.getElementById('audioProcessing');
            
            try {
                // 创建测试音频数据
                const sampleRate = 48000;
                const duration = 1; // 1秒
                const samples = sampleRate * duration;
                const audioData = new Float32Array(samples);
                
                // 生成测试音频（440Hz正弦波）
                for (let i = 0; i < samples; i++) {
                    audioData[i] = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.5;
                }
                
                // 测试重采样到16kHz
                const targetSampleRate = 16000;
                const resampledData = resampleAudio(audioData, sampleRate, targetSampleRate);
                
                // 测试转换为PCM16
                const pcm16Data = floatToPCM16(resampledData);
                
                // 测试Base64编码
                const base64Data = arrayBufferToBase64(pcm16Data.buffer);
                
                let html = '<h3>音频处理测试结果:</h3>';
                html += `<div class="success">✓ 原始数据: ${audioData.length} 样本 (${sampleRate}Hz)</div>`;
                html += `<div class="success">✓ 重采样后: ${resampledData.length} 样本 (${targetSampleRate}Hz)</div>`;
                html += `<div class="success">✓ PCM16转换: ${pcm16Data.length} 字节</div>`;
                html += `<div class="success">✓ Base64编码: ${base64Data.length} 字符</div>`;
                
                result.innerHTML = html;
                log('音频处理测试完成', 'success');
                
            } catch (error) {
                result.innerHTML = `<div class="error">✗ 音频处理测试失败: ${error.message}</div>`;
                log(`音频处理测试失败: ${error.message}`, 'error');
            }
        }

        // 音频处理辅助函数
        function resampleAudio(inputData, inputSampleRate, outputSampleRate) {
            const ratio = inputSampleRate / outputSampleRate;
            const outputLength = Math.floor(inputData.length / ratio);
            const outputData = new Float32Array(outputLength);
            
            for (let i = 0; i < outputLength; i++) {
                const index = i * ratio;
                const indexFloor = Math.floor(index);
                const indexCeil = Math.min(indexFloor + 1, inputData.length - 1);
                const fraction = index - indexFloor;
                
                outputData[i] = inputData[indexFloor] * (1 - fraction) + inputData[indexCeil] * fraction;
            }
            
            return outputData;
        }

        function floatToPCM16(floatData) {
            const pcmData = new Int16Array(floatData.length);
            for (let i = 0; i < floatData.length; i++) {
                const sample = Math.max(-1, Math.min(1, floatData[i]));
                pcmData[i] = sample * 32767;
            }
            return pcmData;
        }

        function arrayBufferToBase64(buffer) {
            const uint8Array = new Uint8Array(buffer);
            let binary = '';
            for (let i = 0; i < uint8Array.length; i++) {
                binary += String.fromCharCode(uint8Array[i]);
            }
            return btoa(binary);
        }

        // 6. WebSocket连接测试
        function testWebSocketConnection() {
            log('开始WebSocket连接测试');
            const result = document.getElementById('webSocketConnection');
            const apiKey = document.getElementById('apiKeyInput').value;
            
            if (!apiKey) {
                result.innerHTML = '<div class="error">请输入API密钥</div>';
                log('API密钥为空', 'error');
                return;
            }
            
            try {
                // 注意：这里使用测试URL，实际使用时需要替换为真实的豆包API地址
                const testUrl = 'wss://echo.websocket.org/';
                webSocketClient = new WebSocket(testUrl);
                
                webSocketClient.onopen = () => {
                    result.innerHTML = '<div class="success">✓ WebSocket连接成功</div>';
                    log('WebSocket连接成功', 'success');
                    
                    // 发送测试消息
                    const testMessage = {
                        type: 'test',
                        timestamp: Date.now(),
                        apiKey: apiKey
                    };
                    webSocketClient.send(JSON.stringify(testMessage));
                };
                
                webSocketClient.onmessage = (event) => {
                    log(`收到WebSocket消息: ${event.data}`, 'info');
                };
                
                webSocketClient.onerror = (error) => {
                    result.innerHTML = '<div class="error">✗ WebSocket连接错误</div>';
                    log('WebSocket连接错误', 'error');
                };
                
                webSocketClient.onclose = () => {
                    result.innerHTML = '<div class="warning">WebSocket连接已关闭</div>';
                    log('WebSocket连接已关闭', 'info');
                };
                
            } catch (error) {
                result.innerHTML = `<div class="error">✗ WebSocket测试失败: ${error.message}</div>`;
                log(`WebSocket测试失败: ${error.message}`, 'error');
            }
        }

        function disconnectWebSocket() {
            if (webSocketClient) {
                webSocketClient.close();
                webSocketClient = null;
                log('WebSocket连接已断开');
            }
        }

        // 7. 本地存储测试
        function testLocalStorage() {
            log('开始本地存储测试');
            const result = document.getElementById('localStorage');
            
            try {
                const testKey = 'realTranTest';
                const testData = {
                    timestamp: Date.now(),
                    settings: {
                        theme: 'light',
                        language: 'zh-CN'
                    }
                };
                
                // 测试写入
                localStorage.setItem(testKey, JSON.stringify(testData));
                
                // 测试读取
                const retrievedData = JSON.parse(localStorage.getItem(testKey));
                
                // 测试删除
                localStorage.removeItem(testKey);
                
                if (retrievedData && retrievedData.timestamp === testData.timestamp) {
                    result.innerHTML = '<div class="success">✓ 本地存储功能正常</div>';
                    log('本地存储测试成功', 'success');
                } else {
                    result.innerHTML = '<div class="error">✗ 本地存储数据不一致</div>';
                    log('本地存储数据不一致', 'error');
                }
                
            } catch (error) {
                result.innerHTML = `<div class="error">✗ 本地存储测试失败: ${error.message}</div>`;
                log(`本地存储测试失败: ${error.message}`, 'error');
            }
        }

        // 8. PWA功能测试
        function testPWAFeatures() {
            log('开始PWA功能测试');
            const result = document.getElementById('pwaFeatures');
            
            let html = '<h3>PWA功能检测:</h3>';
            
            // Service Worker支持
            if ('serviceWorker' in navigator) {
                html += '<div class="success">✓ Service Worker 支持</div>';
                log('Service Worker 支持', 'success');
            } else {
                html += '<div class="error">✗ Service Worker 不支持</div>';
                log('Service Worker 不支持', 'error');
            }
            
            // 安装提示支持
            if (window.BeforeInstallPromptEvent) {
                html += '<div class="success">✓ 安装提示 支持</div>';
                log('安装提示 支持', 'success');
            } else {
                html += '<div class="warning">⚠ 安装提示 可能不支持</div>';
                log('安装提示 可能不支持', 'warning');
            }
            
            // 显示模式检测
            if (window.matchMedia('(display-mode: standalone)').matches) {
                html += '<div class="success">✓ 当前运行在PWA模式</div>';
                log('当前运行在PWA模式', 'success');
            } else {
                html += '<div class="warning">⚠ 当前运行在浏览器模式</div>';
                log('当前运行在浏览器模式', 'info');
            }
            
            result.innerHTML = html;
            log('PWA功能测试完成');
        }

        // 9. 性能测试
        function testPerformance() {
            log('开始性能测试');
            const result = document.getElementById('performance');
            
            const startTime = performance.now();
            
            // 测试大量DOM操作
            const testContainer = document.createElement('div');
            for (let i = 0; i < 1000; i++) {
                const element = document.createElement('div');
                element.textContent = `Test element ${i}`;
                testContainer.appendChild(element);
            }
            
            const domTime = performance.now() - startTime;
            
            // 测试音频处理性能
            const audioStartTime = performance.now();
            const audioData = new Float32Array(48000); // 1秒音频数据
            for (let i = 0; i < audioData.length; i++) {
                audioData[i] = Math.sin(2 * Math.PI * 440 * i / 48000);
            }
            const audioTime = performance.now() - audioStartTime;
            
            // 内存使用情况
            let memoryInfo = '';
            if (performance.memory) {
                const memory = performance.memory;
                memoryInfo = `
                    <div class="success">内存使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB</div>
                    <div class="success">内存限制: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB</div>
                `;
            }
            
            let html = '<h3>性能测试结果:</h3>';
            html += `<div class="success">DOM操作耗时: ${domTime.toFixed(2)} ms</div>`;
            html += `<div class="success">音频处理耗时: ${audioTime.toFixed(2)} ms</div>`;
            html += memoryInfo;
            
            result.innerHTML = html;
            log(`性能测试完成 - DOM: ${domTime.toFixed(2)}ms, 音频: ${audioTime.toFixed(2)}ms`, 'success');
        }

        // 页面加载完成后自动运行基础检测
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动检测');
            testBrowserSupport();
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            stopAudioCapture();
            disconnectWebSocket();
        });
    </script>
</body>
</html>
