/**
 * 主应用程序
 * 整合所有模块，实现完整的同声传译功能
 */
class RealTranApp {
    constructor() {
        this.audioCapture = null;
        this.audioProcessor = null;
        this.webSocketClient = null;
        this.uiController = null;
        this.isInitialized = false;
        this.isTranslating = false;
    }

    /**
     * 初始化应用程序
     */
    async initialize() {
        try {
            console.log('正在初始化应用程序...');

            // 创建模块实例
            this.audioCapture = new AudioCapture();
            this.audioProcessor = new AudioProcessor();
            this.webSocketClient = new WebSocketClient();
            this.uiController = new UIController();

            // 初始化UI控制器
            this.uiController.initialize();

            // 绑定事件监听器
            this.bindEvents();

            // 初始化音频捕获
            await this.audioCapture.initialize();

            this.isInitialized = true;
            console.log('应用程序初始化完成');

        } catch (error) {
            console.error('应用程序初始化失败:', error);
            this.uiController?.showError(`初始化失败: ${error.message}`);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // UI控制器事件
        this.uiController.on('startRecording', () => {
            this.startTranslation();
        });

        this.uiController.on('stopRecording', () => {
            this.stopTranslation();
        });

        this.uiController.on('sourceLanguageChanged', (language) => {
            this.updateTranslationConfig({ source_language: language });
        });

        this.uiController.on('targetLanguageChanged', (language) => {
            this.updateTranslationConfig({ target_language: language });
        });

        this.uiController.on('languagesSwapped', (languages) => {
            this.updateTranslationConfig({
                source_language: languages.source,
                target_language: languages.target
            });
        });

        this.uiController.on('apiKeyChanged', (apiKey) => {
            this.webSocketClient.setApiKey(apiKey);
        });

        this.uiController.on('audioDeviceChanged', (deviceId) => {
            this.switchAudioDevice(deviceId);
        });

        // 音频捕获事件
        this.audioCapture.on('captureStarted', (stream) => {
            console.log('音频捕获已开始');
            this.setupAudioProcessing(stream);
        });

        this.audioCapture.on('captureStopped', () => {
            console.log('音频捕获已停止');
            this.audioProcessor.stopProcessing();
        });

        this.audioCapture.on('volumeChange', (volume) => {
            this.uiController.updateVolumeLevel(volume);
        });

        this.audioCapture.on('speechDetected', () => {
            console.log('检测到语音');
        });

        this.audioCapture.on('silenceDetected', () => {
            console.log('检测到静音');
        });

        this.audioCapture.on('devicesUpdated', (devices) => {
            this.uiController.updateAudioDevices(devices);
        });

        this.audioCapture.on('error', (error) => {
            console.error('音频捕获错误:', error);
            this.uiController.showError(error.message);
        });

        // 音频处理事件
        this.audioProcessor.on('audioProcessed', (pcmData) => {
            if (this.isTranslating && this.webSocketClient.isConnectedToServer()) {
                this.webSocketClient.sendAudioData(pcmData);
            }
        });

        this.audioProcessor.on('error', (error) => {
            console.error('音频处理错误:', error);
            this.uiController.showError(`音频处理错误: ${error.message}`);
        });

        // WebSocket客户端事件
        this.webSocketClient.on('connecting', () => {
            this.uiController.updateConnectionStatus(false, '正在连接...');
            this.uiController.showLoading('正在连接服务器...');
        });

        this.webSocketClient.on('connected', () => {
            this.uiController.updateConnectionStatus(true, '已连接');
            this.uiController.hideLoading();
            this.uiController.showSuccess('连接成功');
        });

        this.webSocketClient.on('authenticated', () => {
            console.log('认证成功');
        });

        this.webSocketClient.on('disconnected', (info) => {
            this.uiController.updateConnectionStatus(false, '连接断开');
            this.uiController.hideLoading();

            if (!info.wasClean) {
                this.uiController.showError('连接意外断开，正在尝试重连...');
            }
        });

        this.webSocketClient.on('translation', (result) => {
            this.handleTranslationResult(result);
        });

        this.webSocketClient.on('error', (error) => {
            console.error('WebSocket错误:', error);
            this.uiController.updateConnectionStatus(false, '连接错误');
            this.uiController.hideLoading();
            this.uiController.showError(error.message);
        });
    }

    /**
     * 设置音频处理
     * @param {MediaStream} stream 音频流
     */
    async setupAudioProcessing(stream) {
        try {
            const audioContext = this.audioCapture.getAudioContext();
            await this.audioProcessor.initialize(audioContext);
            this.audioProcessor.startProcessing(stream);
        } catch (error) {
            console.error('设置音频处理失败:', error);
            this.uiController.showError(`音频处理设置失败: ${error.message}`);
        }
    }

    /**
     * 开始翻译
     */
    async startTranslation() {
        try {
            if (this.isTranslating) {
                console.warn('翻译已在进行中');
                return;
            }

            // 检查API密钥
            const apiKey = this.uiController.elements.apiKey?.value;
            if (!apiKey) {
                this.uiController.showError('请先设置API密钥');
                this.uiController.openSettings();
                return;
            }

            this.uiController.showLoading('正在启动翻译...');

            // 设置WebSocket配置
            this.webSocketClient.setApiKey(apiKey);
            this.updateTranslationConfig();

            // 连接WebSocket
            if (!this.webSocketClient.isConnectedToServer()) {
                await this.webSocketClient.connect();
            }

            // 开始音频捕获
            await this.audioCapture.startCapture();

            this.isTranslating = true;
            this.uiController.hideLoading();
            this.uiController.showSuccess('翻译已开始');

        } catch (error) {
            console.error('开始翻译失败:', error);
            this.uiController.hideLoading();
            this.uiController.showError(`启动失败: ${error.message}`);
            this.isTranslating = false;
        }
    }

    /**
     * 停止翻译
     */
    stopTranslation() {
        try {
            this.isTranslating = false;

            // 停止音频捕获
            this.audioCapture.stopCapture();

            // 停止音频处理
            this.audioProcessor.stopProcessing();

            this.uiController.showSuccess('翻译已停止');

        } catch (error) {
            console.error('停止翻译失败:', error);
            this.uiController.showError(`停止失败: ${error.message}`);
        }
    }

    /**
     * 更新翻译配置
     * @param {Object} config 配置更新
     */
    updateTranslationConfig(config = {}) {
        const state = this.uiController.getState();

        const fullConfig = {
            source_language: state.sourceLanguage,
            target_language: state.targetLanguage,
            audio_format: 'pcm',
            sample_rate: 16000,
            channel: 1,
            bit_depth: 16,
            ...config
        };

        this.webSocketClient.setConfig(fullConfig);
    }

    /**
     * 处理翻译结果
     * @param {Object} result 翻译结果
     */
    handleTranslationResult(result) {
        try {
            // 显示原文
            if (result.sourceText) {
                this.uiController.addSourceText(result.sourceText, result.isFinal);
            }

            // 显示译文
            if (result.targetText) {
                this.uiController.addTargetText(result.targetText, result.isFinal);
            }

            // 播放提示音（如果启用）
            if (result.isFinal && this.uiController.getState().playSound) {
                this.playNotificationSound();
            }

        } catch (error) {
            console.error('处理翻译结果失败:', error);
        }
    }

    /**
     * 切换音频设备
     * @param {string} deviceId 设备ID
     */
    async switchAudioDevice(deviceId) {
        try {
            if (this.isTranslating) {
                await this.audioCapture.switchDevice(deviceId);
            }
        } catch (error) {
            console.error('切换音频设备失败:', error);
            this.uiController.showError(`切换设备失败: ${error.message}`);
        }
    }

    /**
     * 播放提示音
     */
    playNotificationSound() {
        try {
            // 创建简单的提示音
            const audioContext = this.audioCapture.getAudioContext();
            if (audioContext) {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            }
        } catch (error) {
            console.error('播放提示音失败:', error);
        }
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isTranslating: this.isTranslating,
            isConnected: this.webSocketClient?.isConnectedToServer() || false,
            isCapturing: this.audioCapture?.isActive() || false
        };
    }

    /**
     * 销毁应用程序
     */
    destroy() {
        try {
            this.stopTranslation();

            // 断开WebSocket连接
            this.webSocketClient?.disconnect();

            // 销毁各个模块
            this.audioCapture?.destroy();
            this.audioProcessor?.destroy();
            this.webSocketClient?.destroy();
            this.uiController?.destroy();

            console.log('应用程序已销毁');

        } catch (error) {
            console.error('销毁应用程序失败:', error);
        }
    }
}

// 全局应用实例
let app = null;

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    try {
        app = new RealTranApp();
        await app.initialize();

        // 将应用实例暴露到全局（用于调试）
        window.realTranApp = app;

    } catch (error) {
        console.error('应用启动失败:', error);
        showError(`应用启动失败: ${error.message}`);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (app) {
        app.destroy();
    }
});

// 处理未捕获的错误
window.addEventListener('error', (event) => {
    console.error('未捕获的错误:', event.error);
    if (app && app.uiController) {
        app.uiController.showError('发生未知错误，请刷新页面重试');
    }
});

// 处理未捕获的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    if (app && app.uiController) {
        app.uiController.showError('发生异步错误，请检查网络连接');
    }
});

// PWA支持
let deferredPrompt = null;

// 监听PWA安装提示
window.addEventListener('beforeinstallprompt', (e) => {
    // 阻止默认的安装提示
    e.preventDefault();
    deferredPrompt = e;

    // 显示自定义安装按钮（可选）
    showInstallPrompt();
});

// 显示安装提示
function showInstallPrompt() {
    if (deferredPrompt && !window.matchMedia('(display-mode: standalone)').matches) {
        // 可以在这里显示自定义的安装提示
        console.log('PWA可以安装');
    }
}

// 安装PWA
async function installPWA() {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        console.log(`用户选择: ${outcome}`);
        deferredPrompt = null;
    }
}

// 注册Service Worker
if ('serviceWorker' in navigator) {
    window.addEventListener('load', async () => {
        try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker注册成功:', registration.scope);

            // 监听更新
            registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        // 有新版本可用
                        if (app && app.uiController) {
                            app.uiController.showSuccess('发现新版本，刷新页面即可更新');
                        }
                    }
                });
            });
        } catch (error) {
            console.log('Service Worker注册失败:', error);
        }
    });
}

// 导出应用类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealTranApp;
}
