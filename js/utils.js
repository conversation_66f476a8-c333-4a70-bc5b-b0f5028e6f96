// 工具函数库

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(this, args);
    };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * 格式化时间
 * @param {number} seconds 秒数
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 检查浏览器支持
 * @returns {Object} 支持情况对象
 */
function checkBrowserSupport() {
    return {
        getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
        webAudio: !!(window.AudioContext || window.webkitAudioContext),
        webSocket: !!window.WebSocket,
        webWorker: !!window.Worker,
        localStorage: !!window.localStorage
    };
}

/**
 * 显示错误提示
 * @param {string} message 错误消息
 * @param {number} duration 显示时长（毫秒）
 */
function showError(message, duration = 5000) {
    const errorToast = document.getElementById('errorToast');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = message;
    errorToast.classList.add('show');
    
    setTimeout(() => {
        errorToast.classList.remove('show');
    }, duration);
}

/**
 * 显示成功提示
 * @param {string} message 成功消息
 * @param {number} duration 显示时长（毫秒）
 */
function showSuccess(message, duration = 3000) {
    const successToast = document.getElementById('successToast');
    const successMessage = document.getElementById('successMessage');
    
    successMessage.textContent = message;
    successToast.classList.add('show');
    
    setTimeout(() => {
        successToast.classList.remove('show');
    }, duration);
}

/**
 * 显示加载指示器
 * @param {string} text 加载文本
 */
function showLoading(text = '正在加载...') {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const loadingText = loadingIndicator.querySelector('.loading-text');
    
    loadingText.textContent = text;
    loadingIndicator.classList.add('show');
}

/**
 * 隐藏加载指示器
 */
function hideLoading() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.classList.remove('show');
}

/**
 * 本地存储工具
 */
const Storage = {
    /**
     * 设置存储项
     * @param {string} key 键
     * @param {any} value 值
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Storage set error:', error);
        }
    },
    
    /**
     * 获取存储项
     * @param {string} key 键
     * @param {any} defaultValue 默认值
     * @returns {any} 存储的值或默认值
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Storage get error:', error);
            return defaultValue;
        }
    },
    
    /**
     * 删除存储项
     * @param {string} key 键
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Storage remove error:', error);
        }
    },
    
    /**
     * 清空所有存储
     */
    clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('Storage clear error:', error);
        }
    }
};

/**
 * 事件发射器
 */
class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    /**
     * 监听事件
     * @param {string} event 事件名
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    /**
     * 移除事件监听
     * @param {string} event 事件名
     * @param {Function} callback 回调函数
     */
    off(event, callback) {
        if (!this.events[event]) return;
        
        this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
    
    /**
     * 触发事件
     * @param {string} event 事件名
     * @param {...any} args 参数
     */
    emit(event, ...args) {
        if (!this.events[event]) return;
        
        this.events[event].forEach(callback => {
            try {
                callback(...args);
            } catch (error) {
                console.error('Event callback error:', error);
            }
        });
    }
    
    /**
     * 只监听一次事件
     * @param {string} event 事件名
     * @param {Function} callback 回调函数
     */
    once(event, callback) {
        const onceCallback = (...args) => {
            callback(...args);
            this.off(event, onceCallback);
        };
        this.on(event, onceCallback);
    }
}

/**
 * 语言配置
 */
const Languages = {
    zh: { name: '中文', code: 'zh-CN' },
    en: { name: 'English', code: 'en-US' },
    ja: { name: '日本語', code: 'ja-JP' },
    ko: { name: '한국어', code: 'ko-KR' },
    fr: { name: 'Français', code: 'fr-FR' },
    de: { name: 'Deutsch', code: 'de-DE' },
    es: { name: 'Español', code: 'es-ES' },
    ru: { name: 'Русский', code: 'ru-RU' }
};

/**
 * 获取语言名称
 * @param {string} code 语言代码
 * @returns {string} 语言名称
 */
function getLanguageName(code) {
    return Languages[code]?.name || code;
}

/**
 * 获取语言完整代码
 * @param {string} code 语言代码
 * @returns {string} 完整语言代码
 */
function getLanguageCode(code) {
    return Languages[code]?.code || code;
}

/**
 * 复制文本到剪贴板
 * @param {string} text 要复制的文本
 * @returns {Promise<boolean>} 是否成功
 */
async function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const result = document.execCommand('copy');
            document.body.removeChild(textArea);
            return result;
        }
    } catch (error) {
        console.error('Copy to clipboard failed:', error);
        return false;
    }
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * 检查是否为触控设备
 * @returns {boolean} 是否为触控设备
 */
function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

// 导出工具函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        debounce,
        throttle,
        deepClone,
        formatTime,
        checkBrowserSupport,
        showError,
        showSuccess,
        showLoading,
        hideLoading,
        Storage,
        EventEmitter,
        Languages,
        getLanguageName,
        getLanguageCode,
        copyToClipboard,
        isMobile,
        isTouchDevice
    };
}
