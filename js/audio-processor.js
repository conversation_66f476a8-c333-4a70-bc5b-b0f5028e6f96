/**
 * 音频处理模块
 * 负责音频格式转换、重采样、PCM编码等功能
 */
class AudioProcessor extends EventEmitter {
    constructor() {
        super();
        this.audioContext = null;
        this.scriptProcessor = null;
        this.sourceNode = null;
        this.isProcessing = false;
        this.targetSampleRate = 16000; // 豆包API要求的采样率
        this.bufferSize = 4096; // 处理缓冲区大小
        this.audioBuffer = []; // 音频数据缓冲区
        this.worker = null; // Web Worker用于重采样
    }

    /**
     * 初始化音频处理器
     * @param {AudioContext} audioContext 音频上下文
     */
    async initialize(audioContext) {
        try {
            this.audioContext = audioContext;
            
            // 创建Web Worker用于音频处理（如果支持）
            if (window.Worker) {
                this.createAudioWorker();
            }
            
            this.emit('initialized');
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * 创建音频处理Web Worker
     */
    createAudioWorker() {
        try {
            // 创建内联Worker
            const workerCode = `
                // 音频重采样Worker
                self.onmessage = function(e) {
                    const { audioData, sourceSampleRate, targetSampleRate } = e.data;
                    
                    if (audioData && sourceSampleRate && targetSampleRate) {
                        const resampled = resampleAudio(audioData, sourceSampleRate, targetSampleRate);
                        const pcmData = floatToPCM16(resampled);
                        
                        self.postMessage({
                            type: 'resampled',
                            data: pcmData
                        });
                    }
                };
                
                // 音频重采样函数
                function resampleAudio(audioBuffer, sourceSampleRate, targetSampleRate) {
                    if (sourceSampleRate === targetSampleRate) {
                        return audioBuffer;
                    }
                    
                    const ratio = sourceSampleRate / targetSampleRate;
                    const newLength = Math.round(audioBuffer.length / ratio);
                    const result = new Float32Array(newLength);
                    
                    for (let i = 0; i < newLength; i++) {
                        const index = i * ratio;
                        const indexInt = Math.floor(index);
                        const indexFrac = index - indexInt;
                        
                        if (indexInt < audioBuffer.length - 1) {
                            // 线性插值
                            result[i] = audioBuffer[indexInt] * (1 - indexFrac) + 
                                       audioBuffer[indexInt + 1] * indexFrac;
                        } else if (indexInt < audioBuffer.length) {
                            result[i] = audioBuffer[indexInt];
                        }
                    }
                    
                    return result;
                }
                
                // 浮点数转PCM16
                function floatToPCM16(floatArray) {
                    const pcmArray = new Int16Array(floatArray.length);
                    for (let i = 0; i < floatArray.length; i++) {
                        // 限制在-1到1之间
                        const sample = Math.max(-1, Math.min(1, floatArray[i]));
                        // 转换为16位整数
                        pcmArray[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
                    }
                    return pcmArray;
                }
            `;
            
            const blob = new Blob([workerCode], { type: 'application/javascript' });
            this.worker = new Worker(URL.createObjectURL(blob));
            
            this.worker.onmessage = (e) => {
                if (e.data.type === 'resampled') {
                    this.emit('audioProcessed', e.data.data);
                }
            };
            
            this.worker.onerror = (error) => {
                console.error('Audio Worker error:', error);
                this.worker = null;
            };
            
        } catch (error) {
            console.error('创建Audio Worker失败:', error);
            this.worker = null;
        }
    }

    /**
     * 开始处理音频流
     * @param {MediaStream} stream 音频流
     */
    startProcessing(stream) {
        try {
            if (this.isProcessing) {
                console.warn('音频处理已在进行中');
                return;
            }

            // 创建音频源节点
            this.sourceNode = this.audioContext.createMediaStreamSource(stream);
            
            // 创建脚本处理器节点
            this.scriptProcessor = this.audioContext.createScriptProcessor(
                this.bufferSize, 1, 1
            );
            
            // 设置音频处理回调
            this.scriptProcessor.onaudioprocess = (event) => {
                this.processAudioData(event);
            };
            
            // 连接音频节点
            this.sourceNode.connect(this.scriptProcessor);
            this.scriptProcessor.connect(this.audioContext.destination);
            
            this.isProcessing = true;
            this.emit('processingStarted');
            
        } catch (error) {
            console.error('开始音频处理失败:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * 处理音频数据
     * @param {AudioProcessingEvent} event 音频处理事件
     */
    processAudioData(event) {
        try {
            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0); // 获取单声道数据
            const sourceSampleRate = inputBuffer.sampleRate;
            
            // 复制音频数据（避免引用问题）
            const audioData = new Float32Array(inputData);
            
            // 如果有Web Worker，使用Worker处理
            if (this.worker) {
                this.worker.postMessage({
                    audioData: audioData,
                    sourceSampleRate: sourceSampleRate,
                    targetSampleRate: this.targetSampleRate
                });
            } else {
                // 主线程处理
                this.processAudioInMainThread(audioData, sourceSampleRate);
            }
            
        } catch (error) {
            console.error('处理音频数据失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 在主线程中处理音频
     * @param {Float32Array} audioData 音频数据
     * @param {number} sourceSampleRate 源采样率
     */
    processAudioInMainThread(audioData, sourceSampleRate) {
        try {
            // 重采样
            const resampledData = this.resampleAudio(
                audioData, 
                sourceSampleRate, 
                this.targetSampleRate
            );
            
            // 转换为PCM16格式
            const pcmData = this.floatToPCM16(resampledData);
            
            // 发送处理后的数据
            this.emit('audioProcessed', pcmData);
            
        } catch (error) {
            console.error('主线程音频处理失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 音频重采样
     * @param {Float32Array} audioBuffer 音频缓冲区
     * @param {number} sourceSampleRate 源采样率
     * @param {number} targetSampleRate 目标采样率
     * @returns {Float32Array} 重采样后的音频数据
     */
    resampleAudio(audioBuffer, sourceSampleRate, targetSampleRate) {
        if (sourceSampleRate === targetSampleRate) {
            return audioBuffer;
        }
        
        const ratio = sourceSampleRate / targetSampleRate;
        const newLength = Math.round(audioBuffer.length / ratio);
        const result = new Float32Array(newLength);
        
        for (let i = 0; i < newLength; i++) {
            const index = i * ratio;
            const indexInt = Math.floor(index);
            const indexFrac = index - indexInt;
            
            if (indexInt < audioBuffer.length - 1) {
                // 线性插值重采样
                result[i] = audioBuffer[indexInt] * (1 - indexFrac) + 
                           audioBuffer[indexInt + 1] * indexFrac;
            } else if (indexInt < audioBuffer.length) {
                result[i] = audioBuffer[indexInt];
            }
        }
        
        return result;
    }

    /**
     * 浮点数转PCM16格式
     * @param {Float32Array} floatArray 浮点数组
     * @returns {Int16Array} PCM16数组
     */
    floatToPCM16(floatArray) {
        const pcmArray = new Int16Array(floatArray.length);
        
        for (let i = 0; i < floatArray.length; i++) {
            // 限制在-1到1之间
            const sample = Math.max(-1, Math.min(1, floatArray[i]));
            // 转换为16位整数
            pcmArray[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }
        
        return pcmArray;
    }

    /**
     * PCM16转Base64
     * @param {Int16Array} pcmData PCM数据
     * @returns {string} Base64编码的字符串
     */
    pcmToBase64(pcmData) {
        try {
            const uint8Array = new Uint8Array(pcmData.buffer);
            let binary = '';
            for (let i = 0; i < uint8Array.length; i++) {
                binary += String.fromCharCode(uint8Array[i]);
            }
            return btoa(binary);
        } catch (error) {
            console.error('PCM转Base64失败:', error);
            return '';
        }
    }

    /**
     * 停止音频处理
     */
    stopProcessing() {
        try {
            this.isProcessing = false;
            
            // 断开音频节点
            if (this.sourceNode) {
                this.sourceNode.disconnect();
                this.sourceNode = null;
            }
            
            if (this.scriptProcessor) {
                this.scriptProcessor.disconnect();
                this.scriptProcessor = null;
            }
            
            // 清空缓冲区
            this.audioBuffer = [];
            
            this.emit('processingStopped');
            
        } catch (error) {
            console.error('停止音频处理失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 设置目标采样率
     * @param {number} sampleRate 采样率
     */
    setTargetSampleRate(sampleRate) {
        this.targetSampleRate = sampleRate;
    }

    /**
     * 设置缓冲区大小
     * @param {number} size 缓冲区大小
     */
    setBufferSize(size) {
        // 缓冲区大小必须是2的幂
        const validSizes = [256, 512, 1024, 2048, 4096, 8192, 16384];
        if (validSizes.includes(size)) {
            this.bufferSize = size;
        } else {
            console.warn('无效的缓冲区大小，使用默认值4096');
        }
    }

    /**
     * 获取处理状态
     * @returns {boolean} 是否正在处理
     */
    isActive() {
        return this.isProcessing;
    }

    /**
     * 销毁音频处理器
     */
    destroy() {
        this.stopProcessing();
        
        if (this.worker) {
            this.worker.terminate();
            this.worker = null;
        }
        
        this.removeAllListeners();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioProcessor;
}
