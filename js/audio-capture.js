/**
 * 音频捕获模块
 * 负责获取麦克风权限、捕获音频流、监控音量等功能
 */
class AudioCapture extends EventEmitter {
    constructor() {
        super();
        this.stream = null;
        this.audioContext = null;
        this.analyser = null;
        this.microphone = null;
        this.dataArray = null;
        this.isCapturing = false;
        this.devices = [];
        this.currentDeviceId = null;
        this.volumeThreshold = 0.01; // 音量阈值
        this.silenceTimeout = null;
        this.silenceDelay = 2000; // 2秒静音检测
    }

    /**
     * 初始化音频捕获
     */
    async initialize() {
        try {
            // 检查浏览器支持
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('您的浏览器不支持音频捕获功能');
            }

            // 创建音频上下文
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (!AudioContext) {
                throw new Error('您的浏览器不支持Web Audio API');
            }

            this.audioContext = new AudioContext();
            
            // 获取音频设备列表
            await this.getAudioDevices();
            
            this.emit('initialized');
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * 获取音频设备列表
     */
    async getAudioDevices() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            this.devices = devices.filter(device => device.kind === 'audioinput');
            this.emit('devicesUpdated', this.devices);
            return this.devices;
        } catch (error) {
            console.error('获取音频设备失败:', error);
            return [];
        }
    }

    /**
     * 请求麦克风权限并开始捕获
     * @param {string} deviceId 设备ID（可选）
     */
    async startCapture(deviceId = null) {
        try {
            if (this.isCapturing) {
                console.warn('音频捕获已在进行中');
                return;
            }

            // 构建约束条件
            const constraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 48000, // 高质量采样率
                    channelCount: 1 // 单声道
                }
            };

            // 如果指定了设备ID
            if (deviceId) {
                constraints.audio.deviceId = { exact: deviceId };
                this.currentDeviceId = deviceId;
            }

            // 获取媒体流
            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // 设置音频分析
            await this.setupAudioAnalysis();
            
            this.isCapturing = true;
            this.emit('captureStarted', this.stream);
            
            // 开始音量监控
            this.startVolumeMonitoring();
            
        } catch (error) {
            this.handleCaptureError(error);
            throw error;
        }
    }

    /**
     * 设置音频分析
     */
    async setupAudioAnalysis() {
        try {
            // 恢复音频上下文（如果被暂停）
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            // 创建音频源
            this.microphone = this.audioContext.createMediaStreamSource(this.stream);
            
            // 创建分析器
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 256;
            this.analyser.smoothingTimeConstant = 0.8;
            
            // 连接音频节点
            this.microphone.connect(this.analyser);
            
            // 创建数据数组
            const bufferLength = this.analyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);
            
        } catch (error) {
            console.error('设置音频分析失败:', error);
            throw error;
        }
    }

    /**
     * 开始音量监控
     */
    startVolumeMonitoring() {
        const monitorVolume = () => {
            if (!this.isCapturing || !this.analyser) {
                return;
            }

            // 获取频域数据
            this.analyser.getByteFrequencyData(this.dataArray);
            
            // 计算平均音量
            let sum = 0;
            for (let i = 0; i < this.dataArray.length; i++) {
                sum += this.dataArray[i];
            }
            const average = sum / this.dataArray.length;
            const volume = average / 255; // 归一化到0-1
            
            // 发送音量事件
            this.emit('volumeChange', volume);
            
            // 静音检测
            this.detectSilence(volume);
            
            // 继续监控
            requestAnimationFrame(monitorVolume);
        };
        
        monitorVolume();
    }

    /**
     * 静音检测
     * @param {number} volume 当前音量
     */
    detectSilence(volume) {
        if (volume < this.volumeThreshold) {
            // 开始静音计时
            if (!this.silenceTimeout) {
                this.silenceTimeout = setTimeout(() => {
                    this.emit('silenceDetected');
                    this.silenceTimeout = null;
                }, this.silenceDelay);
            }
        } else {
            // 有声音，清除静音计时
            if (this.silenceTimeout) {
                clearTimeout(this.silenceTimeout);
                this.silenceTimeout = null;
                this.emit('speechDetected');
            }
        }
    }

    /**
     * 停止音频捕获
     */
    stopCapture() {
        try {
            this.isCapturing = false;
            
            // 清除静音检测
            if (this.silenceTimeout) {
                clearTimeout(this.silenceTimeout);
                this.silenceTimeout = null;
            }
            
            // 停止媒体流
            if (this.stream) {
                this.stream.getTracks().forEach(track => {
                    track.stop();
                });
                this.stream = null;
            }
            
            // 断开音频节点
            if (this.microphone) {
                this.microphone.disconnect();
                this.microphone = null;
            }
            
            if (this.analyser) {
                this.analyser.disconnect();
                this.analyser = null;
            }
            
            this.emit('captureStopped');
            
        } catch (error) {
            console.error('停止音频捕获失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 切换音频设备
     * @param {string} deviceId 新设备ID
     */
    async switchDevice(deviceId) {
        try {
            const wasCapturing = this.isCapturing;
            
            // 停止当前捕获
            if (wasCapturing) {
                this.stopCapture();
            }
            
            // 使用新设备开始捕获
            if (wasCapturing) {
                await this.startCapture(deviceId);
            }
            
            this.currentDeviceId = deviceId;
            this.emit('deviceSwitched', deviceId);
            
        } catch (error) {
            console.error('切换音频设备失败:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * 处理捕获错误
     * @param {Error} error 错误对象
     */
    handleCaptureError(error) {
        let message = '音频捕获失败';
        
        if (error.name === 'NotAllowedError') {
            message = '请允许访问麦克风权限';
        } else if (error.name === 'NotFoundError') {
            message = '未找到可用的麦克风设备';
        } else if (error.name === 'NotReadableError') {
            message = '麦克风设备被其他应用占用';
        } else if (error.name === 'OverconstrainedError') {
            message = '指定的音频约束无法满足';
        } else if (error.name === 'SecurityError') {
            message = '安全限制阻止了麦克风访问';
        }
        
        console.error('音频捕获错误:', error);
        this.emit('error', new Error(message));
    }

    /**
     * 获取当前音频流
     * @returns {MediaStream|null} 音频流
     */
    getStream() {
        return this.stream;
    }

    /**
     * 获取音频上下文
     * @returns {AudioContext|null} 音频上下文
     */
    getAudioContext() {
        return this.audioContext;
    }

    /**
     * 检查是否正在捕获
     * @returns {boolean} 是否正在捕获
     */
    isActive() {
        return this.isCapturing;
    }

    /**
     * 设置音量阈值
     * @param {number} threshold 阈值（0-1）
     */
    setVolumeThreshold(threshold) {
        this.volumeThreshold = Math.max(0, Math.min(1, threshold));
    }

    /**
     * 设置静音检测延迟
     * @param {number} delay 延迟时间（毫秒）
     */
    setSilenceDelay(delay) {
        this.silenceDelay = Math.max(500, delay);
    }

    /**
     * 销毁音频捕获实例
     */
    destroy() {
        this.stopCapture();
        
        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }
        
        this.removeAllListeners();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioCapture;
}
