/**
 * UI控制器模块
 * 负责用户界面交互逻辑、状态管理、事件处理等功能
 */
class UIController extends EventEmitter {
    constructor() {
        super();
        this.elements = {};
        this.state = {
            isRecording: false,
            isConnected: false,
            sourceLanguage: 'en',
            targetLanguage: 'zh',
            theme: 'light',
            fontSize: 16,
            autoScroll: true,
            playSound: true
        };
        this.sourceTextBuffer = '';
        this.targetTextBuffer = '';
        this.scrollTimeout = null;
    }

    /**
     * 初始化UI控制器
     */
    initialize() {
        try {
            // 获取DOM元素
            this.getElements();
            
            // 绑定事件监听器
            this.bindEvents();
            
            // 加载保存的设置
            this.loadSettings();
            
            // 初始化UI状态
            this.updateUI();
            
            // 检查浏览器支持
            this.checkBrowserSupport();
            
            this.emit('initialized');
        } catch (error) {
            console.error('UI控制器初始化失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 获取DOM元素
     */
    getElements() {
        const elementIds = [
            'connectionStatus', 'statusIndicator', 'statusText', 'audioLevel', 'levelBar',
            'themeToggle', 'settingsToggle', 'sourceLanguage', 'targetLanguage', 'languageSwap',
            'recordButton', 'stopButton', 'sourceText', 'targetText', 'clearSource', 'clearTarget',
            'copyTarget', 'settingsPanel', 'closeSettings', 'overlay', 'apiKey', 'audioDevice',
            'autoScroll', 'playSound', 'fontSize', 'fontSizeValue', 'errorToast', 'errorMessage',
            'errorClose', 'successToast', 'successMessage', 'loadingIndicator'
        ];

        elementIds.forEach(id => {
            this.elements[id] = document.getElementById(id);
            if (!this.elements[id]) {
                console.warn(`元素 ${id} 未找到`);
            }
        });
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主题切换
        if (this.elements.themeToggle) {
            this.elements.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // 设置面板
        if (this.elements.settingsToggle) {
            this.elements.settingsToggle.addEventListener('click', () => {
                this.toggleSettings();
            });
        }

        if (this.elements.closeSettings) {
            this.elements.closeSettings.addEventListener('click', () => {
                this.closeSettings();
            });
        }

        if (this.elements.overlay) {
            this.elements.overlay.addEventListener('click', () => {
                this.closeSettings();
            });
        }

        // 语言选择
        if (this.elements.sourceLanguage) {
            this.elements.sourceLanguage.addEventListener('change', (e) => {
                this.setSourceLanguage(e.target.value);
            });
        }

        if (this.elements.targetLanguage) {
            this.elements.targetLanguage.addEventListener('change', (e) => {
                this.setTargetLanguage(e.target.value);
            });
        }

        if (this.elements.languageSwap) {
            this.elements.languageSwap.addEventListener('click', () => {
                this.swapLanguages();
            });
        }

        // 录音控制
        if (this.elements.recordButton) {
            this.elements.recordButton.addEventListener('click', () => {
                this.startRecording();
            });
        }

        if (this.elements.stopButton) {
            this.elements.stopButton.addEventListener('click', () => {
                this.stopRecording();
            });
        }

        // 文本操作
        if (this.elements.clearSource) {
            this.elements.clearSource.addEventListener('click', () => {
                this.clearSourceText();
            });
        }

        if (this.elements.clearTarget) {
            this.elements.clearTarget.addEventListener('click', () => {
                this.clearTargetText();
            });
        }

        if (this.elements.copyTarget) {
            this.elements.copyTarget.addEventListener('click', () => {
                this.copyTargetText();
            });
        }

        // 设置项
        if (this.elements.apiKey) {
            this.elements.apiKey.addEventListener('input', debounce((e) => {
                this.setApiKey(e.target.value);
            }, 500));
        }

        if (this.elements.audioDevice) {
            this.elements.audioDevice.addEventListener('change', (e) => {
                this.setAudioDevice(e.target.value);
            });
        }

        if (this.elements.autoScroll) {
            this.elements.autoScroll.addEventListener('change', (e) => {
                this.setAutoScroll(e.target.checked);
            });
        }

        if (this.elements.playSound) {
            this.elements.playSound.addEventListener('change', (e) => {
                this.setPlaySound(e.target.checked);
            });
        }

        if (this.elements.fontSize) {
            this.elements.fontSize.addEventListener('input', (e) => {
                this.setFontSize(parseInt(e.target.value));
            });
        }

        // 错误提示关闭
        if (this.elements.errorClose) {
            this.elements.errorClose.addEventListener('click', () => {
                this.hideError();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });

        // 窗口大小变化
        window.addEventListener('resize', debounce(() => {
            this.handleResize();
        }, 250));
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeyboard(e) {
        // Ctrl/Cmd + Enter: 开始/停止录音
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (this.state.isRecording) {
                this.stopRecording();
            } else {
                this.startRecording();
            }
        }

        // Ctrl/Cmd + ,: 打开设置
        if ((e.ctrlKey || e.metaKey) && e.key === ',') {
            e.preventDefault();
            this.toggleSettings();
        }

        // Escape: 关闭设置
        if (e.key === 'Escape') {
            this.closeSettings();
        }

        // Ctrl/Cmd + C: 复制译文（当焦点在译文区域时）
        if ((e.ctrlKey || e.metaKey) && e.key === 'c' && 
            document.activeElement === this.elements.targetText) {
            e.preventDefault();
            this.copyTargetText();
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 移动端适配
        if (window.innerWidth <= 768) {
            this.adaptForMobile();
        } else {
            this.adaptForDesktop();
        }
    }

    /**
     * 移动端适配
     */
    adaptForMobile() {
        // 关闭设置面板（如果打开）
        if (this.elements.settingsPanel && this.elements.settingsPanel.classList.contains('open')) {
            this.closeSettings();
        }
    }

    /**
     * 桌面端适配
     */
    adaptForDesktop() {
        // 桌面端特殊处理
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const newTheme = this.state.theme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    /**
     * 设置主题
     * @param {string} theme 主题名称
     */
    setTheme(theme) {
        this.state.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        
        // 更新主题图标
        if (this.elements.themeToggle) {
            const icon = this.elements.themeToggle.querySelector('.theme-icon');
            if (icon) {
                icon.textContent = theme === 'light' ? '🌙' : '☀️';
            }
        }
        
        this.saveSettings();
        this.emit('themeChanged', theme);
    }

    /**
     * 切换设置面板
     */
    toggleSettings() {
        if (this.elements.settingsPanel.classList.contains('open')) {
            this.closeSettings();
        } else {
            this.openSettings();
        }
    }

    /**
     * 打开设置面板
     */
    openSettings() {
        this.elements.settingsPanel.classList.add('open');
        this.elements.overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
        this.emit('settingsOpened');
    }

    /**
     * 关闭设置面板
     */
    closeSettings() {
        this.elements.settingsPanel.classList.remove('open');
        this.elements.overlay.classList.remove('active');
        document.body.style.overflow = '';
        this.emit('settingsClosed');
    }

    /**
     * 设置源语言
     * @param {string} language 语言代码
     */
    setSourceLanguage(language) {
        this.state.sourceLanguage = language;
        this.saveSettings();
        this.emit('sourceLanguageChanged', language);
    }

    /**
     * 设置目标语言
     * @param {string} language 语言代码
     */
    setTargetLanguage(language) {
        this.state.targetLanguage = language;
        this.saveSettings();
        this.emit('targetLanguageChanged', language);
    }

    /**
     * 交换语言
     */
    swapLanguages() {
        const temp = this.state.sourceLanguage;
        this.state.sourceLanguage = this.state.targetLanguage;
        this.state.targetLanguage = temp;
        
        // 更新UI
        if (this.elements.sourceLanguage) {
            this.elements.sourceLanguage.value = this.state.sourceLanguage;
        }
        if (this.elements.targetLanguage) {
            this.elements.targetLanguage.value = this.state.targetLanguage;
        }
        
        this.saveSettings();
        this.emit('languagesSwapped', {
            source: this.state.sourceLanguage,
            target: this.state.targetLanguage
        });
    }

    /**
     * 开始录音
     */
    startRecording() {
        if (this.state.isRecording) return;
        
        this.state.isRecording = true;
        this.updateRecordingUI();
        this.emit('startRecording');
    }

    /**
     * 停止录音
     */
    stopRecording() {
        if (!this.state.isRecording) return;
        
        this.state.isRecording = false;
        this.updateRecordingUI();
        this.emit('stopRecording');
    }

    /**
     * 更新录音UI状态
     */
    updateRecordingUI() {
        if (this.elements.recordButton) {
            this.elements.recordButton.disabled = this.state.isRecording;
        }
        if (this.elements.stopButton) {
            this.elements.stopButton.disabled = !this.state.isRecording;
        }
        
        // 添加录音状态样式
        if (this.state.isRecording) {
            this.elements.recordButton?.classList.add('recording');
        } else {
            this.elements.recordButton?.classList.remove('recording');
        }
    }

    /**
     * 更新连接状态
     * @param {boolean} connected 是否已连接
     * @param {string} status 状态文本
     */
    updateConnectionStatus(connected, status = '') {
        this.state.isConnected = connected;
        
        if (this.elements.statusIndicator) {
            this.elements.statusIndicator.className = 'status-indicator';
            if (connected) {
                this.elements.statusIndicator.classList.add('connected');
            } else if (status === 'connecting') {
                this.elements.statusIndicator.classList.add('connecting');
            }
        }
        
        if (this.elements.statusText) {
            this.elements.statusText.textContent = status || (connected ? '已连接' : '未连接');
        }
    }

    /**
     * 更新音量指示器
     * @param {number} volume 音量值 (0-1)
     */
    updateVolumeLevel(volume) {
        if (this.elements.levelBar) {
            const percentage = Math.min(100, volume * 100);
            this.elements.levelBar.style.width = `${percentage}%`;
        }
    }

    /**
     * 添加源文本
     * @param {string} text 文本内容
     * @param {boolean} isFinal 是否为最终结果
     */
    addSourceText(text, isFinal = false) {
        if (isFinal) {
            this.sourceTextBuffer += text + '\n';
        } else {
            // 临时显示，不添加到缓冲区
        }
        
        this.updateTextDisplay('source', this.sourceTextBuffer + (isFinal ? '' : text));
        
        if (this.state.autoScroll) {
            this.scrollToBottom('source');
        }
    }

    /**
     * 添加目标文本
     * @param {string} text 文本内容
     * @param {boolean} isFinal 是否为最终结果
     */
    addTargetText(text, isFinal = false) {
        if (isFinal) {
            this.targetTextBuffer += text + '\n';
        } else {
            // 临时显示，不添加到缓冲区
        }
        
        this.updateTextDisplay('target', this.targetTextBuffer + (isFinal ? '' : text));
        
        if (this.state.autoScroll) {
            this.scrollToBottom('target');
        }
    }

    /**
     * 更新文本显示
     * @param {string} type 文本类型 ('source' | 'target')
     * @param {string} text 文本内容
     */
    updateTextDisplay(type, text) {
        const element = type === 'source' ? this.elements.sourceText : this.elements.targetText;
        if (element) {
            if (text.trim()) {
                element.innerHTML = this.formatText(text);
                element.classList.remove('placeholder');
            } else {
                element.innerHTML = `<div class="placeholder">${type === 'source' ? '等待语音输入...' : '翻译结果将显示在这里...'}</div>`;
            }
        }
    }

    /**
     * 格式化文本
     * @param {string} text 原始文本
     * @returns {string} 格式化后的HTML
     */
    formatText(text) {
        return text
            .split('\n')
            .filter(line => line.trim())
            .map(line => `<p>${this.escapeHtml(line)}</p>`)
            .join('');
    }

    /**
     * 转义HTML
     * @param {string} text 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 滚动到底部
     * @param {string} type 文本类型
     */
    scrollToBottom(type) {
        const element = type === 'source' ? this.elements.sourceText : this.elements.targetText;
        if (element) {
            // 防抖滚动
            clearTimeout(this.scrollTimeout);
            this.scrollTimeout = setTimeout(() => {
                element.scrollTop = element.scrollHeight;
            }, 100);
        }
    }

    /**
     * 清空源文本
     */
    clearSourceText() {
        this.sourceTextBuffer = '';
        this.updateTextDisplay('source', '');
        this.emit('sourceTextCleared');
    }

    /**
     * 清空目标文本
     */
    clearTargetText() {
        this.targetTextBuffer = '';
        this.updateTextDisplay('target', '');
        this.emit('targetTextCleared');
    }

    /**
     * 复制目标文本
     */
    async copyTargetText() {
        try {
            const success = await copyToClipboard(this.targetTextBuffer.trim());
            if (success) {
                showSuccess('译文已复制到剪贴板');
            } else {
                showError('复制失败，请手动选择文本复制');
            }
        } catch (error) {
            console.error('复制失败:', error);
            showError('复制失败');
        }
    }

    /**
     * 设置API密钥
     * @param {string} apiKey API密钥
     */
    setApiKey(apiKey) {
        this.saveSettings();
        this.emit('apiKeyChanged', apiKey);
    }

    /**
     * 设置音频设备
     * @param {string} deviceId 设备ID
     */
    setAudioDevice(deviceId) {
        this.saveSettings();
        this.emit('audioDeviceChanged', deviceId);
    }

    /**
     * 设置自动滚动
     * @param {boolean} enabled 是否启用
     */
    setAutoScroll(enabled) {
        this.state.autoScroll = enabled;
        this.saveSettings();
    }

    /**
     * 设置提示音
     * @param {boolean} enabled 是否启用
     */
    setPlaySound(enabled) {
        this.state.playSound = enabled;
        this.saveSettings();
    }

    /**
     * 设置字体大小
     * @param {number} size 字体大小
     */
    setFontSize(size) {
        this.state.fontSize = size;
        
        // 更新显示
        if (this.elements.fontSizeValue) {
            this.elements.fontSizeValue.textContent = `${size}px`;
        }
        
        // 应用字体大小
        document.documentElement.style.setProperty('--font-size-base', `${size}px`);
        
        this.saveSettings();
    }

    /**
     * 更新音频设备列表
     * @param {Array} devices 设备列表
     */
    updateAudioDevices(devices) {
        if (!this.elements.audioDevice) return;
        
        // 清空现有选项
        this.elements.audioDevice.innerHTML = '<option value="">默认麦克风</option>';
        
        // 添加设备选项
        devices.forEach(device => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            option.textContent = device.label || `麦克风 ${device.deviceId.slice(0, 8)}`;
            this.elements.audioDevice.appendChild(option);
        });
    }

    /**
     * 显示错误
     * @param {string} message 错误消息
     */
    showError(message) {
        showError(message);
    }

    /**
     * 隐藏错误
     */
    hideError() {
        if (this.elements.errorToast) {
            this.elements.errorToast.classList.remove('show');
        }
    }

    /**
     * 显示成功消息
     * @param {string} message 成功消息
     */
    showSuccess(message) {
        showSuccess(message);
    }

    /**
     * 显示加载状态
     * @param {string} text 加载文本
     */
    showLoading(text) {
        showLoading(text);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        hideLoading();
    }

    /**
     * 检查浏览器支持
     */
    checkBrowserSupport() {
        const support = checkBrowserSupport();
        
        if (!support.getUserMedia) {
            this.showError('您的浏览器不支持音频录制功能');
        }
        
        if (!support.webAudio) {
            this.showError('您的浏览器不支持Web Audio API');
        }
        
        if (!support.webSocket) {
            this.showError('您的浏览器不支持WebSocket');
        }
    }

    /**
     * 加载设置
     */
    loadSettings() {
        const settings = Storage.get('realTranSettings', {});
        
        // 应用设置
        if (settings.theme) {
            this.setTheme(settings.theme);
        }
        
        if (settings.sourceLanguage) {
            this.state.sourceLanguage = settings.sourceLanguage;
            if (this.elements.sourceLanguage) {
                this.elements.sourceLanguage.value = settings.sourceLanguage;
            }
        }
        
        if (settings.targetLanguage) {
            this.state.targetLanguage = settings.targetLanguage;
            if (this.elements.targetLanguage) {
                this.elements.targetLanguage.value = settings.targetLanguage;
            }
        }
        
        if (settings.fontSize) {
            this.setFontSize(settings.fontSize);
            if (this.elements.fontSize) {
                this.elements.fontSize.value = settings.fontSize;
            }
        }
        
        if (typeof settings.autoScroll === 'boolean') {
            this.state.autoScroll = settings.autoScroll;
            if (this.elements.autoScroll) {
                this.elements.autoScroll.checked = settings.autoScroll;
            }
        }
        
        if (typeof settings.playSound === 'boolean') {
            this.state.playSound = settings.playSound;
            if (this.elements.playSound) {
                this.elements.playSound.checked = settings.playSound;
            }
        }
        
        if (settings.apiKey && this.elements.apiKey) {
            this.elements.apiKey.value = settings.apiKey;
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        const settings = {
            theme: this.state.theme,
            sourceLanguage: this.state.sourceLanguage,
            targetLanguage: this.state.targetLanguage,
            fontSize: this.state.fontSize,
            autoScroll: this.state.autoScroll,
            playSound: this.state.playSound
        };
        
        // 保存API密钥
        if (this.elements.apiKey && this.elements.apiKey.value) {
            settings.apiKey = this.elements.apiKey.value;
        }
        
        Storage.set('realTranSettings', settings);
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        this.updateRecordingUI();
        this.updateConnectionStatus(this.state.isConnected);
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 销毁UI控制器
     */
    destroy() {
        // 清理定时器
        if (this.scrollTimeout) {
            clearTimeout(this.scrollTimeout);
        }
        
        // 移除事件监听器
        this.removeAllListeners();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIController;
}
