/**
 * WebSocket客户端模块
 * 负责与豆包同传API的WebSocket连接、认证、数据传输等功能
 */
class WebSocketClient extends EventEmitter {
    constructor() {
        super();
        this.ws = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.apiKey = '';
        this.apiUrl = 'wss://openspeech.bytedance.com/api/v1/sti';
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 初始重连延迟
        this.heartbeatInterval = null;
        this.heartbeatTimeout = 30000; // 30秒心跳
        this.requestId = 0;
        this.pendingRequests = new Map();
        this.config = {
            source_language: 'en',
            target_language: 'zh',
            audio_format: 'pcm',
            sample_rate: 16000,
            channel: 1,
            bit_depth: 16
        };
    }

    /**
     * 设置API密钥
     * @param {string} apiKey API密钥
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }

    /**
     * 设置翻译配置
     * @param {Object} config 配置对象
     */
    setConfig(config) {
        this.config = { ...this.config, ...config };
    }

    /**
     * 连接WebSocket
     */
    async connect() {
        if (this.isConnected || this.isConnecting) {
            console.warn('WebSocket已连接或正在连接中');
            return;
        }

        if (!this.apiKey) {
            throw new Error('请先设置API密钥');
        }

        try {
            this.isConnecting = true;
            this.emit('connecting');

            // 创建WebSocket连接
            this.ws = new WebSocket(this.apiUrl);
            
            // 设置二进制数据类型
            this.ws.binaryType = 'arraybuffer';

            // 设置事件监听器
            this.setupEventListeners();

            // 等待连接建立
            await this.waitForConnection();

        } catch (error) {
            this.isConnecting = false;
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * 设置WebSocket事件监听器
     */
    setupEventListeners() {
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            this.isConnected = true;
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            
            // 发送认证信息
            this.authenticate();
            
            // 开始心跳
            this.startHeartbeat();
            
            this.emit('connected');
        };

        this.ws.onmessage = (event) => {
            this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket连接已关闭', event.code, event.reason);
            this.handleDisconnection(event);
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.emit('error', new Error('WebSocket连接错误'));
        };
    }

    /**
     * 等待连接建立
     */
    waitForConnection() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('连接超时'));
            }, 10000);

            const onConnected = () => {
                clearTimeout(timeout);
                this.off('connected', onConnected);
                this.off('error', onError);
                resolve();
            };

            const onError = (error) => {
                clearTimeout(timeout);
                this.off('connected', onConnected);
                this.off('error', onError);
                reject(error);
            };

            this.once('connected', onConnected);
            this.once('error', onError);
        });
    }

    /**
     * 发送认证信息
     */
    authenticate() {
        try {
            const authMessage = {
                type: 'auth',
                data: {
                    api_key: this.apiKey,
                    timestamp: Date.now(),
                    ...this.config
                }
            };

            this.sendMessage(authMessage);
        } catch (error) {
            console.error('发送认证信息失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 开始心跳
     */
    startHeartbeat() {
        this.stopHeartbeat(); // 清除之前的心跳
        
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.sendMessage({
                    type: 'ping',
                    timestamp: Date.now()
                });
            }
        }, this.heartbeatTimeout);
    }

    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * 处理接收到的消息
     * @param {MessageEvent} event 消息事件
     */
    handleMessage(event) {
        try {
            let data;
            
            // 处理不同类型的数据
            if (typeof event.data === 'string') {
                data = JSON.parse(event.data);
            } else if (event.data instanceof ArrayBuffer) {
                // 处理二进制数据（如果API返回音频）
                data = {
                    type: 'audio',
                    data: event.data
                };
            } else {
                console.warn('未知的消息格式:', event.data);
                return;
            }

            // 根据消息类型处理
            switch (data.type) {
                case 'auth_response':
                    this.handleAuthResponse(data);
                    break;
                case 'translation':
                    this.handleTranslation(data);
                    break;
                case 'audio':
                    this.handleAudioResponse(data);
                    break;
                case 'error':
                    this.handleServerError(data);
                    break;
                case 'pong':
                    // 心跳响应，无需处理
                    break;
                default:
                    console.log('收到未知类型消息:', data);
            }

        } catch (error) {
            console.error('处理消息失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 处理认证响应
     * @param {Object} data 响应数据
     */
    handleAuthResponse(data) {
        if (data.success) {
            console.log('认证成功');
            this.emit('authenticated');
        } else {
            console.error('认证失败:', data.message);
            this.emit('error', new Error(`认证失败: ${data.message}`));
        }
    }

    /**
     * 处理翻译结果
     * @param {Object} data 翻译数据
     */
    handleTranslation(data) {
        this.emit('translation', {
            sourceText: data.source_text || '',
            targetText: data.target_text || '',
            isFinal: data.is_final || false,
            confidence: data.confidence || 0,
            timestamp: data.timestamp || Date.now()
        });
    }

    /**
     * 处理音频响应
     * @param {Object} data 音频数据
     */
    handleAudioResponse(data) {
        this.emit('audioResponse', data.data);
    }

    /**
     * 处理服务器错误
     * @param {Object} data 错误数据
     */
    handleServerError(data) {
        const error = new Error(data.message || '服务器错误');
        error.code = data.code;
        this.emit('error', error);
    }

    /**
     * 发送音频数据
     * @param {ArrayBuffer|Int16Array} audioData 音频数据
     */
    sendAudioData(audioData) {
        if (!this.isConnected) {
            console.warn('WebSocket未连接，无法发送音频数据');
            return;
        }

        try {
            // 构建音频消息
            const message = {
                type: 'audio',
                request_id: this.generateRequestId(),
                timestamp: Date.now(),
                data: this.arrayBufferToBase64(audioData)
            };

            this.sendMessage(message);
        } catch (error) {
            console.error('发送音频数据失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 发送消息
     * @param {Object} message 消息对象
     */
    sendMessage(message) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket未连接');
        }

        try {
            const messageStr = JSON.stringify(message);
            this.ws.send(messageStr);
        } catch (error) {
            console.error('发送消息失败:', error);
            throw error;
        }
    }

    /**
     * ArrayBuffer转Base64
     * @param {ArrayBuffer|Int16Array} buffer 缓冲区
     * @returns {string} Base64字符串
     */
    arrayBufferToBase64(buffer) {
        try {
            let uint8Array;
            
            if (buffer instanceof Int16Array) {
                uint8Array = new Uint8Array(buffer.buffer);
            } else if (buffer instanceof ArrayBuffer) {
                uint8Array = new Uint8Array(buffer);
            } else {
                throw new Error('不支持的数据类型');
            }

            let binary = '';
            for (let i = 0; i < uint8Array.length; i++) {
                binary += String.fromCharCode(uint8Array[i]);
            }
            
            return btoa(binary);
        } catch (error) {
            console.error('转换Base64失败:', error);
            return '';
        }
    }

    /**
     * 生成请求ID
     * @returns {string} 请求ID
     */
    generateRequestId() {
        return `req_${++this.requestId}_${Date.now()}`;
    }

    /**
     * 处理连接断开
     * @param {CloseEvent} event 关闭事件
     */
    handleDisconnection(event) {
        this.isConnected = false;
        this.isConnecting = false;
        this.stopHeartbeat();
        
        this.emit('disconnected', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean
        });

        // 自动重连（除非是正常关闭）
        if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }

    /**
     * 安排重连
     */
    scheduleReconnect() {
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
        
        console.log(`${delay}ms后尝试重连 (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.reconnectAttempts++;
            this.connect().catch(error => {
                console.error('重连失败:', error);
            });
        }, delay);
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, '正常关闭');
            this.ws = null;
        }
        
        this.isConnected = false;
        this.isConnecting = false;
        this.stopHeartbeat();
        this.pendingRequests.clear();
    }

    /**
     * 获取连接状态
     * @returns {boolean} 是否已连接
     */
    isConnectedToServer() {
        return this.isConnected;
    }

    /**
     * 设置重连参数
     * @param {number} maxAttempts 最大重连次数
     * @param {number} delay 初始延迟
     */
    setReconnectConfig(maxAttempts, delay) {
        this.maxReconnectAttempts = maxAttempts;
        this.reconnectDelay = delay;
    }

    /**
     * 销毁WebSocket客户端
     */
    destroy() {
        this.disconnect();
        this.removeAllListeners();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebSocketClient;
}
