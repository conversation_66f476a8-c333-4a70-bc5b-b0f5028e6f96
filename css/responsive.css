/* 响应式设计 - 移动优先 */

/* 超小屏幕 (手机竖屏) */
@media (max-width: 480px) {
    .status-bar {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .status-info {
        gap: var(--spacing-md);
    }

    .audio-level {
        display: none; /* 在小屏幕上隐藏音量指示器 */
    }

    .main-content {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }

    .control-panel {
        padding: var(--spacing-lg);
    }

    .language-selector {
        flex-direction: column;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .language-group {
        width: 100%;
    }

    .language-swap {
        align-self: center;
        margin-top: 0;
        order: 2;
    }

    .main-controls {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .record-button,
    .stop-button {
        width: 100%;
        padding: var(--spacing-lg);
        font-size: var(--font-size-base);
        min-height: 56px; /* 确保触控目标足够大 */
    }

    /* 文本面板在小屏幕上垂直排列 */
    .text-panels {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .text-panel {
        min-height: 200px;
    }

    .panel-header {
        padding: var(--spacing-md);
    }

    .panel-title {
        font-size: var(--font-size-base);
    }

    .text-content {
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}

/* 小屏幕 (手机横屏/小平板) */
@media (min-width: 481px) and (max-width: 768px) {
    .main-content {
        padding: var(--spacing-lg);
    }

    .language-selector {
        flex-wrap: wrap;
        justify-content: center;
    }

    .language-group {
        min-width: 140px;
    }

    .main-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .text-panels {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .text-panel {
        min-height: 250px;
    }
}

/* 中等屏幕 (平板) */
@media (min-width: 769px) and (max-width: 1024px) {
    .main-content {
        max-width: 900px;
    }

    .text-panels {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }

    .text-panel {
        min-height: 350px;
    }
}

/* 大屏幕 (桌面) */
@media (min-width: 1025px) {
    .status-bar {
        padding: var(--spacing-lg) var(--spacing-xl);
    }

    .main-content {
        padding: var(--spacing-xl);
        gap: var(--spacing-2xl);
    }

    .control-panel {
        padding: var(--spacing-2xl);
    }

    .language-selector {
        max-width: 600px;
        margin: 0 auto var(--spacing-2xl);
    }

    .main-controls {
        max-width: 400px;
        margin: 0 auto;
    }

    .text-panels {
        min-height: 500px;
    }

    .text-panel {
        min-height: 500px;
    }

    /* 桌面端悬停效果 */
    .language-select:hover {
        border-color: var(--border-hover);
    }

    .clear-button:hover,
    .copy-button:hover {
        transform: scale(1.1);
    }
}

/* 超大屏幕 */
@media (min-width: 1440px) {
    .main-content {
        max-width: 1400px;
        padding: var(--spacing-2xl) var(--spacing-xl);
    }

    .text-panels {
        gap: var(--spacing-2xl);
    }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 600px) {
    .main-content {
        gap: var(--spacing-md);
    }

    .control-panel {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .language-selector {
        margin-bottom: var(--spacing-md);
    }

    .text-panels {
        min-height: 250px;
    }

    .text-panel {
        min-height: 250px;
    }
}

/* 高分辨率屏幕适配 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .status-indicator {
        width: 10px;
        height: 10px;
    }

    .level-meter {
        height: 6px;
    }
}

/* 触控设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增大触控目标 */
    .theme-toggle,
    .settings-toggle {
        width: 48px;
        height: 48px;
    }

    .language-swap {
        width: 48px;
        height: 48px;
    }

    .clear-button,
    .copy-button {
        width: 40px;
        height: 40px;
    }

    .record-button,
    .stop-button {
        min-height: 56px;
        padding: var(--spacing-lg) var(--spacing-xl);
    }

    /* 移除悬停效果 */
    .record-button:hover,
    .stop-button:hover,
    .language-swap:hover,
    .theme-toggle:hover,
    .settings-toggle:hover,
    .clear-button:hover,
    .copy-button:hover {
        transform: none;
    }

    /* 添加触控反馈 */
    .record-button:active,
    .stop-button:active {
        transform: scale(0.98);
    }

    .language-swap:active,
    .theme-toggle:active,
    .settings-toggle:active,
    .clear-button:active,
    .copy-button:active {
        transform: scale(0.95);
        background-color: var(--bg-tertiary);
    }
}

/* 键盘导航优化 */
@media (hover: hover) and (pointer: fine) {
    /* 键盘焦点样式 */
    button:focus-visible,
    select:focus-visible,
    input:focus-visible {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }

    /* 鼠标悬停效果 */
    .record-button:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .stop-button:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .status-indicator.connecting {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
        --bg-secondary: #ffffff;
        --bg-tertiary: #f0f0f0;
    }

    .text-panel {
        border: 2px solid var(--border-color);
    }

    .record-button,
    .stop-button {
        border: 2px solid currentColor;
    }
}

/* 暗色主题偏好 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #64748b;
        --border-color: #334155;
        --border-hover: #475569;
    }
}

/* 打印样式 */
@media print {
    .status-bar,
    .control-panel,
    .panel-header {
        display: none;
    }

    .main-content {
        padding: 0;
        gap: var(--spacing-md);
    }

    .text-panels {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .text-panel {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
    }

    .text-content {
        color: #000;
        font-size: 12pt;
        line-height: 1.5;
    }
}

/* PWA和移动端特殊优化 */
@media (display-mode: standalone) {
    /* PWA模式下的特殊样式 */
    .app-container {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
    }

    .status-bar {
        padding-top: calc(env(safe-area-inset-top) + 0.5rem);
    }
}

/* iOS Safari特殊处理 */
@supports (-webkit-touch-callout: none) {
    .app-container {
        min-height: -webkit-fill-available;
    }

    /* 防止iOS Safari的弹跳效果 */
    body {
        position: fixed;
        overflow: hidden;
        width: 100%;
        height: 100%;
    }

    .app-container {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* 可折叠设备支持 */
@media (spanning: single-fold-vertical) {
    .main-content {
        flex-direction: row;
    }

    .control-panel {
        width: 50vw;
    }

    .translation-area {
        width: 50vw;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .main-content {
        flex-direction: row;
        gap: 1rem;
    }

    .control-panel {
        flex-direction: column;
        width: 200px;
        min-width: 200px;
    }

    .translation-area {
        flex: 1;
        display: flex;
        gap: 1rem;
    }

    .text-section {
        flex: 1;
    }

    .status-bar {
        padding: 0.5rem 1rem;
    }

    .settings-panel {
        width: 90vw;
        max-width: 600px;
    }
}

/* 小屏幕设备特殊优化 */
@media (max-width: 320px) {
    .control-panel {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .record-button,
    .stop-button {
        padding: 8px 12px;
        font-size: 0.875rem;
        min-height: 48px;
    }

    .language-controls {
        flex-direction: column;
        gap: 0.5rem;
    }

    .language-select {
        width: 100%;
    }

    .text-panel {
        min-height: 120px;
    }

    .text-content {
        font-size: 0.875rem;
    }

    .settings-panel {
        width: 95vw;
        padding: 1rem;
    }
}

/* 性能优化 */
@media (prefers-reduced-motion: no-preference) {
    /* 只在用户允许动画时添加复杂动画 */
    .record-button,
    .stop-button {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .settings-panel {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .overlay {
        transition: opacity 0.3s ease;
    }
}

/* 电池优化模式 */
@media (prefers-reduced-motion: reduce) {
    .loading-spinner {
        animation: none;
    }

    .loading-spinner::after {
        content: "⏳";
    }

    .status-indicator.connecting {
        animation: none;
    }

    .status-indicator.connecting::after {
        content: "🔄";
    }
}
