/* 主题样式 */

/* 暗色主题 */
[data-theme="dark"] {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 背景颜色 */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    
    /* 文本颜色 */
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    /* 边框颜色 */
    --border-color: #334155;
    --border-hover: #475569;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

/* 暗色主题特殊样式 */
[data-theme="dark"] .theme-icon::before {
    content: "☀️";
}

[data-theme="dark"] .text-content::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .text-content::-webkit-scrollbar-thumb {
    background: var(--text-muted);
}

[data-theme="dark"] .text-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 亮色主题 */
[data-theme="light"] {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    
    /* 文本颜色 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* 边框颜色 */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

[data-theme="light"] .theme-icon::before {
    content: "🌙";
}

/* 高对比度主题 */
[data-theme="high-contrast"] {
    --primary-color: #0000ff;
    --primary-hover: #0000cc;
    --secondary-color: #666666;
    --success-color: #008000;
    --warning-color: #ff8c00;
    --error-color: #ff0000;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f0f0f0;
    --bg-tertiary: #e0e0e0;
    
    /* 文本颜色 */
    --text-primary: #000000;
    --text-secondary: #333333;
    --text-muted: #666666;
    
    /* 边框颜色 */
    --border-color: #000000;
    --border-hover: #333333;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

[data-theme="high-contrast"] .text-panel,
[data-theme="high-contrast"] .control-panel {
    border: 2px solid var(--border-color);
}

[data-theme="high-contrast"] .record-button,
[data-theme="high-contrast"] .stop-button {
    border: 2px solid currentColor;
    font-weight: 700;
}

/* 设置面板样式 */
.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background-color: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: right var(--transition-normal);
    overflow-y: auto;
}

.settings-panel.open {
    right: 0;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.settings-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.close-settings {
    width: 32px;
    height: 32px;
    border: none;
    background-color: transparent;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-fast);
}

.close-settings:hover {
    background-color: var(--bg-tertiary);
}

.settings-content {
    padding: var(--spacing-lg);
}

.setting-group {
    margin-bottom: var(--spacing-xl);
}

.setting-group label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.setting-input,
.setting-select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-secondary);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.setting-input:focus,
.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.setting-help {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.setting-help a {
    color: var(--primary-color);
    text-decoration: none;
}

.setting-help a:hover {
    text-decoration: underline;
}

.setting-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.setting-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: var(--font-size-sm);
    font-weight: bold;
}

.setting-range {
    width: 100%;
    height: 6px;
    border-radius: var(--radius-sm);
    background-color: var(--bg-tertiary);
    outline: none;
    -webkit-appearance: none;
}

.setting-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.setting-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

.range-value {
    display: inline-block;
    margin-left: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    z-index: 1001;
    display: none;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.loading-indicator.show {
    display: flex;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

/* 提示消息 */
.error-toast,
.success-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.error-toast.show,
.success-toast.show {
    transform: translateX(0);
}

.error-toast {
    background-color: var(--error-color);
    color: white;
}

.success-toast {
    background-color: var(--success-color);
    color: white;
}

.error-content,
.success-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
}

.error-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: var(--font-size-lg);
    margin-left: auto;
}

/* 移动端设置面板适配 */
@media (max-width: 480px) {
    .settings-panel {
        width: 100%;
        right: -100%;
    }
    
    .error-toast,
    .success-toast {
        left: 20px;
        right: 20px;
        max-width: none;
        transform: translateY(-100%);
    }
    
    .error-toast.show,
    .success-toast.show {
        transform: translateY(0);
    }
}
