/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 颜色变量 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    
    /* 文本颜色 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* 边框颜色 */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 字体 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* 过渡 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow-x: hidden;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.status-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--error-color);
    transition: background-color var(--transition-fast);
}

.status-indicator.connected {
    background-color: var(--success-color);
}

.status-indicator.connecting {
    background-color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

.status-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.audio-level {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.level-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.level-meter {
    width: 60px;
    height: 4px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.level-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--error-color));
    width: 0%;
    transition: width var(--transition-fast);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.theme-toggle,
.settings-toggle {
    width: 40px;
    height: 40px;
    border: none;
    background-color: transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-fast);
}

.theme-toggle:hover,
.settings-toggle:hover {
    background-color: var(--bg-tertiary);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* 控制面板 */
.control-panel {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.language-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.language-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.language-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.language-select {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    cursor: pointer;
    transition: border-color var(--transition-fast);
}

.language-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.language-swap {
    width: 44px;
    height: 44px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    margin-top: 1.5rem;
}

.language-swap:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.main-controls {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.record-button,
.stop-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 140px;
    justify-content: center;
}

.record-button {
    background-color: var(--primary-color);
    color: white;
}

.record-button:hover:not(:disabled) {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.stop-button {
    background-color: var(--error-color);
    color: white;
}

.stop-button:hover:not(:disabled) {
    background-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.record-button:disabled,
.stop-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 翻译显示区域 */
.translation-display {
    flex: 1;
    min-height: 400px;
}

.text-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    height: 100%;
}

.text-panel {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-direction: column;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.panel-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.panel-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.clear-button,
.copy-button {
    width: 32px;
    height: 32px;
    border: none;
    background-color: transparent;
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-fast);
}

.clear-button:hover,
.copy-button:hover {
    background-color: var(--bg-primary);
}

.text-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    font-size: var(--font-size-base);
    line-height: 1.7;
    color: var(--text-primary);
}

.placeholder {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* 动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn var(--transition-normal);
}

/* 滚动条样式 */
.text-content::-webkit-scrollbar {
    width: 6px;
}

.text-content::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

.text-content::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: var(--radius-sm);
}

.text-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}
